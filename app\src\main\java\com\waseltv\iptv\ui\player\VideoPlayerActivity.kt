package com.waseltv.iptv.ui.player

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.source.hls.HlsMediaSource
import com.google.android.exoplayer2.ui.PlayerView
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource
import com.google.android.exoplayer2.upstream.DefaultDataSource
import com.google.android.exoplayer2.util.Log
import android.util.Log as AndroidLog
import okhttp3.OkHttpClient
import okhttp3.Request
import com.waseltv.iptv.R
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.ui.theme.WASELTVTheme

class VideoPlayerActivity : ComponentActivity() {
    private var exoPlayer: ExoPlayer? = null
    private var channel: Channel? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Get channel from intent
        channel = intent.getParcelableExtra("channel")
        
        if (channel == null) {
            finish()
            return
        }
        
        // Set landscape orientation
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        // Hide system UI for fullscreen experience
        hideSystemUI()
        
        setContent {
            WASELTVTheme {
                VideoPlayerScreen(
                    channel = channel!!,
                    onBackPressed = { finish() }
                )
            }
        }
    }
    
    private fun hideSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowInsetsControllerCompat(window, window.decorView).let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
    }
    
    override fun onPause() {
        super.onPause()
        exoPlayer?.pause()
    }
    
    override fun onResume() {
        super.onResume()
        exoPlayer?.play()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VideoPlayerScreen(
    channel: Channel,
    onBackPressed: () -> Unit
) {
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    var isTestingUrl by remember { mutableStateOf(true) }

    // اختبار URL قبل تشغيل ExoPlayer
    LaunchedEffect(channel.url) {
        AndroidLog.d("VideoPlayer", "🔍 اختبار URL قبل التشغيل: ${channel.url}")
        isTestingUrl = true

        try {
            val client = okhttp3.OkHttpClient.Builder()
                .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                .build()

            val request = okhttp3.Request.Builder()
                .url(channel.url)
                .head()
                .addHeader("User-Agent", "VLC/3.0.0 LibVLC/3.0.0")
                .build()

            val response = client.newCall(request).execute()

            AndroidLog.d("VideoPlayer", "📊 نتيجة اختبار URL: ${response.code}")
            AndroidLog.d("VideoPlayer", "📋 Headers: ${response.headers}")

            if (response.isSuccessful) {
                AndroidLog.d("VideoPlayer", "✅ URL يعمل، بدء تشغيل ExoPlayer")
                isTestingUrl = false
            } else {
                AndroidLog.e("VideoPlayer", "❌ URL لا يعمل: ${response.code} ${response.message}")
                hasError = true
                errorMessage = "❌ الرابط لا يعمل\n\nكود الخطأ: ${response.code}\nالرسالة: ${response.message}\n\nجرب قناة أخرى"
                isTestingUrl = false
                isLoading = false
            }
        } catch (e: Exception) {
            AndroidLog.e("VideoPlayer", "❌ فشل اختبار URL: ${e.message}", e)
            hasError = true
            errorMessage = "❌ فشل الاتصال بالرابط\n\nالخطأ: ${e.message}\n\n🔍 تحقق من:\n• الاتصال بالإنترنت\n• صحة الرابط\n• حالة السيرفر"
            isTestingUrl = false
            isLoading = false
        }
    }
    
    val exoPlayer = remember(isTestingUrl) {
        if (isTestingUrl || hasError) {
            null // لا ننشئ ExoPlayer حتى ينجح اختبار URL
        } else {
        AndroidLog.d("VideoPlayer", "🚀 إنشاء ExoPlayer للقناة: ${channel.name}")
        AndroidLog.d("VideoPlayer", "🔗 URL: ${channel.url}")

        // إنشاء HTTP DataSource مع headers محسنة لـ Xtream Codes
        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("VLC/3.0.0 LibVLC/3.0.0")  // استخدام User-Agent مشهور
            .setConnectTimeoutMs(15000)  // تقليل timeout للاختبار السريع
            .setReadTimeoutMs(15000)
            .setAllowCrossProtocolRedirects(true)
            .setDefaultRequestProperties(mapOf(
                "Accept" to "*/*",
                "Accept-Language" to "en-US,en;q=0.9,ar;q=0.8",
                "Cache-Control" to "no-cache",
                "Connection" to "keep-alive"
            ))

        // إنشاء DataSource Factory
        val dataSourceFactory = DefaultDataSource.Factory(context, httpDataSourceFactory)

        ExoPlayer.Builder(context)
            .setMediaSourceFactory(
                com.google.android.exoplayer2.source.DefaultMediaSourceFactory(dataSourceFactory)
            )
            .setLoadControl(
                com.google.android.exoplayer2.DefaultLoadControl.Builder()
                    .setBufferDurationsMs(
                        1000,   // Min buffer
                        5000,   // Max buffer
                        500,    // Buffer for playback
                        1000    // Buffer for playback after rebuffer
                    )
                    .build()
            )
            .build().apply {

                // إنشاء MediaItem مع معلومات إضافية
                val mediaItem = MediaItem.Builder()
                    .setUri(channel.url)
                    .setTag(channel.name)
                    .build()

                AndroidLog.d("VideoPlayer", "📺 إعداد MediaItem: ${mediaItem.localConfiguration?.uri}")
                setMediaItem(mediaItem)

                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        AndroidLog.d("VideoPlayer", "حالة التشغيل تغيرت: $playbackState")
                        when (playbackState) {
                            Player.STATE_READY -> {
                                AndroidLog.d("VideoPlayer", "✅ جاهز للتشغيل")
                                isLoading = false
                                hasError = false
                            }
                            Player.STATE_BUFFERING -> {
                                AndroidLog.d("VideoPlayer", "🔄 جاري التحميل...")
                                isLoading = true
                                hasError = false
                            }
                            Player.STATE_ENDED -> {
                                AndroidLog.d("VideoPlayer", "⏹️ انتهى التشغيل")
                                isLoading = false
                            }
                            Player.STATE_IDLE -> {
                                AndroidLog.d("VideoPlayer", "⏸️ في وضع الخمول")
                                isLoading = false
                            }
                        }
                    }

                    override fun onPlayerError(error: com.google.android.exoplayer2.PlaybackException) {
                        AndroidLog.e("VideoPlayer", "❌ خطأ في التشغيل: ${error.message}", error)
                        AndroidLog.e("VideoPlayer", "نوع الخطأ: ${error.errorCode}")
                        AndroidLog.e("VideoPlayer", "السبب: ${error.cause?.message}")
                        AndroidLog.e("VideoPlayer", "URL: ${channel.url}")

                        // طباعة تفاصيل إضافية للتشخيص
                        if (error.cause != null) {
                            AndroidLog.e("VideoPlayer", "تفاصيل السبب: ${error.cause!!.javaClass.simpleName}")
                            AndroidLog.e("VideoPlayer", "رسالة السبب: ${error.cause!!.message}")
                        }

                        isLoading = false
                        hasError = true

                        // رسائل خطأ مفصلة ومفيدة
                        errorMessage = when (error.errorCode) {
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED ->
                                "❌ فشل الاتصال بالشبكة\n\n🔍 الحلول المقترحة:\n• تحقق من الاتصال بالإنترنت\n• جرب شبكة WiFi مختلفة\n• تأكد من عمل السيرفر"
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT ->
                                "⏱️ انتهت مهلة الاتصال\n\n🔍 الحلول المقترحة:\n• الشبكة بطيئة، جرب مرة أخرى\n• تحقق من استقرار الاتصال\n• جرب قناة أخرى"
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED ->
                                "📹 تنسيق الفيديو غير مدعوم\n\n🔍 المشكلة:\n• الـ stream تالف أو غير متاح\n• تنسيق غير مدعوم من ExoPlayer\n• جرب قناة أخرى"
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_BAD_HTTP_STATUS ->
                                "🌐 خطأ في السيرفر\n\n🔍 التفاصيل:\n• ${error.message}\n• السيرفر غير متاح حالياً\n• جرب قناة أخرى أو أعد المحاولة لاحقاً"
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_UNSPECIFIED ->
                                "🔗 مشكلة في الرابط\n\n🔍 الحلول المقترحة:\n• الرابط غير صحيح أو منتهي الصلاحية\n• جرب قناة أخرى\n• تحقق من إعدادات الشبكة"
                            else ->
                                "❌ خطأ في تشغيل القناة\n\n🔍 التفاصيل:\n• ${error.message ?: "خطأ غير معروف"}\n• كود الخطأ: ${error.errorCode}\n• جرب قناة أخرى أو أعد المحاولة"
                        }
                    }

                    override fun onIsLoadingChanged(isLoading: Boolean) {
                        AndroidLog.d("VideoPlayer", "حالة التحميل: $isLoading")
                    }
                })

                // بدء التحضير والتشغيل
                prepare()
                playWhenReady = true

                AndroidLog.d("VideoPlayer", "تم إعداد ExoPlayer وبدء التشغيل")
            }
        }
    }
    
    DisposableEffect(exoPlayer) {
        onDispose {
            exoPlayer?.release()
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Video Player
        if (exoPlayer != null) {
            AndroidView(
                factory = { context ->
                    PlayerView(context).apply {
                        player = exoPlayer
                        useController = true
                        setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // Back button
        IconButton(
            onClick = onBackPressed,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = stringResource(R.string.close),
                tint = Color.White
            )
        }
        
        // Channel info
        Card(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.7f)
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = channel.name,
                    color = Color.White,
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = channel.group ?: "عام",
                    color = Color.White.copy(alpha = 0.7f),
                    style = MaterialTheme.typography.bodySmall
                )
                if (channel.url.contains("maventv.one")) {
                    Text(
                        text = "🔗 Xtream Codes",
                        color = Color.Green,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
        
        // Loading indicator
        if (isLoading || isTestingUrl) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = if (isTestingUrl) "🔍 اختبار الرابط..." else "📺 جاري تحميل القناة...",
                        color = Color.White,
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = if (isTestingUrl) "التحقق من صحة الرابط قبل التشغيل" else "إعداد مشغل الفيديو",
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
        
        // Error message
        if (hasError) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(24.dp)
                ) {
                    Text(
                        text = "❌ خطأ في تشغيل القناة",
                        color = Color.Red,
                        style = MaterialTheme.typography.titleLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = errorMessage,
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "URL: ${channel.url}",
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodySmall
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = {
                            AndroidLog.d("VideoPlayer", "🔄 إعادة محاولة تشغيل القناة: ${channel.name}")
                            hasError = false
                            isLoading = true
                            isTestingUrl = true

                            // إعادة اختبار URL من البداية
                            // سيتم تشغيل LaunchedEffect مرة أخرى
                        }
                    ) {
                        Text("🔄 إعادة المحاولة")
                    }
                }
            }
        }
    }
}
