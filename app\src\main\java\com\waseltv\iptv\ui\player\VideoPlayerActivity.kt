package com.waseltv.iptv.ui.player

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.source.hls.HlsMediaSource
import com.google.android.exoplayer2.ui.PlayerView
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource
import com.google.android.exoplayer2.upstream.DefaultDataSource
import com.google.android.exoplayer2.util.Log
import android.util.Log as AndroidLog
import com.waseltv.iptv.R
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.ui.theme.WASELTVTheme

class VideoPlayerActivity : ComponentActivity() {
    private var exoPlayer: ExoPlayer? = null
    private var channel: Channel? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Get channel from intent
        channel = intent.getParcelableExtra("channel")
        
        if (channel == null) {
            finish()
            return
        }
        
        // Set landscape orientation
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        // Hide system UI for fullscreen experience
        hideSystemUI()
        
        setContent {
            WASELTVTheme {
                VideoPlayerScreen(
                    channel = channel!!,
                    onBackPressed = { finish() }
                )
            }
        }
    }
    
    private fun hideSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowInsetsControllerCompat(window, window.decorView).let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
    }
    
    override fun onPause() {
        super.onPause()
        exoPlayer?.pause()
    }
    
    override fun onResume() {
        super.onResume()
        exoPlayer?.play()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VideoPlayerScreen(
    channel: Channel,
    onBackPressed: () -> Unit
) {
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    
    val exoPlayer = remember {
        AndroidLog.d("VideoPlayer", "إنشاء ExoPlayer للقناة: ${channel.name}")
        AndroidLog.d("VideoPlayer", "URL: ${channel.url}")

        // إنشاء HTTP DataSource مع headers مخصصة لـ Xtream Codes
        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("WASEL-TV/1.0 ExoPlayer")
            .setConnectTimeoutMs(30000)
            .setReadTimeoutMs(30000)
            .setAllowCrossProtocolRedirects(true)

        // إنشاء DataSource Factory
        val dataSourceFactory = DefaultDataSource.Factory(context, httpDataSourceFactory)

        ExoPlayer.Builder(context)
            .setMediaSourceFactory(
                com.google.android.exoplayer2.source.DefaultMediaSourceFactory(dataSourceFactory)
            )
            .build().apply {

                // إنشاء MediaItem مع معلومات إضافية
                val mediaItem = MediaItem.Builder()
                    .setUri(channel.url)
                    .setTag(channel.name)
                    .build()

                setMediaItem(mediaItem)

                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        AndroidLog.d("VideoPlayer", "حالة التشغيل تغيرت: $playbackState")
                        when (playbackState) {
                            Player.STATE_READY -> {
                                AndroidLog.d("VideoPlayer", "✅ جاهز للتشغيل")
                                isLoading = false
                                hasError = false
                            }
                            Player.STATE_BUFFERING -> {
                                AndroidLog.d("VideoPlayer", "🔄 جاري التحميل...")
                                isLoading = true
                                hasError = false
                            }
                            Player.STATE_ENDED -> {
                                AndroidLog.d("VideoPlayer", "⏹️ انتهى التشغيل")
                                isLoading = false
                            }
                            Player.STATE_IDLE -> {
                                AndroidLog.d("VideoPlayer", "⏸️ في وضع الخمول")
                                isLoading = false
                            }
                        }
                    }

                    override fun onPlayerError(error: com.google.android.exoplayer2.PlaybackException) {
                        AndroidLog.e("VideoPlayer", "❌ خطأ في التشغيل: ${error.message}", error)
                        AndroidLog.e("VideoPlayer", "نوع الخطأ: ${error.errorCode}")
                        AndroidLog.e("VideoPlayer", "السبب: ${error.cause?.message}")

                        isLoading = false
                        hasError = true

                        // رسائل خطأ مفصلة
                        errorMessage = when (error.errorCode) {
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED ->
                                "فشل الاتصال بالشبكة. تحقق من الاتصال بالإنترنت."
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT ->
                                "انتهت مهلة الاتصال. جرب مرة أخرى."
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED ->
                                "تنسيق الفيديو غير مدعوم أو تالف."
                            com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_BAD_HTTP_STATUS ->
                                "خطأ في السيرفر (${error.message}). تحقق من صحة الرابط."
                            else ->
                                "خطأ في تشغيل القناة: ${error.message ?: "خطأ غير معروف"}"
                        }
                    }

                    override fun onIsLoadingChanged(isLoading: Boolean) {
                        AndroidLog.d("VideoPlayer", "حالة التحميل: $isLoading")
                    }
                })

                // بدء التحضير والتشغيل
                prepare()
                playWhenReady = true

                AndroidLog.d("VideoPlayer", "تم إعداد ExoPlayer وبدء التشغيل")
            }
    }
    
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Video Player
        AndroidView(
            factory = { context ->
                PlayerView(context).apply {
                    player = exoPlayer
                    useController = true
                    setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                }
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // Back button
        IconButton(
            onClick = onBackPressed,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = stringResource(R.string.close),
                tint = Color.White
            )
        }
        
        // Channel info
        Card(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.7f)
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = channel.name,
                    color = Color.White,
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = channel.group ?: "عام",
                    color = Color.White.copy(alpha = 0.7f),
                    style = MaterialTheme.typography.bodySmall
                )
                if (channel.url.contains("maventv.one")) {
                    Text(
                        text = "🔗 Xtream Codes",
                        color = Color.Green,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
        
        // Loading indicator
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = stringResource(R.string.player_loading),
                        color = Color.White,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
        
        // Error message
        if (hasError) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(24.dp)
                ) {
                    Text(
                        text = "❌ خطأ في تشغيل القناة",
                        color = Color.Red,
                        style = MaterialTheme.typography.titleLarge
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = errorMessage,
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "URL: ${channel.url}",
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodySmall
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = {
                            AndroidLog.d("VideoPlayer", "🔄 إعادة محاولة تشغيل القناة: ${channel.name}")
                            hasError = false
                            isLoading = true

                            // إعادة إنشاء MediaItem وإعداد المشغل
                            val mediaItem = MediaItem.Builder()
                                .setUri(channel.url)
                                .setTag(channel.name)
                                .build()

                            exoPlayer.setMediaItem(mediaItem)
                            exoPlayer.prepare()
                            exoPlayer.playWhenReady = true
                        }
                    ) {
                        Text(stringResource(R.string.retry))
                    }
                }
            }
        }
    }
}
