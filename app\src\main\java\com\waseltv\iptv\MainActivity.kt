package com.waseltv.iptv

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.waseltv.iptv.ui.components.ChannelsList
import com.waseltv.iptv.ui.components.LoadingScreen
import com.waseltv.iptv.ui.components.ErrorScreen
import com.waseltv.iptv.ui.player.VideoPlayerActivity
import com.waseltv.iptv.ui.settings.SettingsActivity
import com.waseltv.iptv.ui.theme.WASELTVTheme
import com.waseltv.iptv.ui.viewmodel.MainViewModel
import com.waseltv.iptv.data.model.Channel

class MainActivity : ComponentActivity() {
    private val viewModel: MainViewModel by viewModels()

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            WASELTVTheme {
                val uiState by viewModel.uiState.collectAsStateWithLifecycle()
                val context = LocalContext.current
                
                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    topBar = {
                        TopAppBar(
                            title = { 
                                Text(
                                    text = stringResource(id = R.string.app_name),
                                    style = MaterialTheme.typography.headlineSmall
                                )
                            },
                            actions = {
                                IconButton(
                                    onClick = {
                                        startActivity(Intent(context, SettingsActivity::class.java))
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Settings,
                                        contentDescription = stringResource(id = R.string.settings)
                                    )
                                }
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = MaterialTheme.colorScheme.primary,
                                titleContentColor = MaterialTheme.colorScheme.onPrimary,
                                actionIconContentColor = MaterialTheme.colorScheme.onPrimary
                            )
                        )
                    }
                ) { paddingValues ->
                    when {
                        uiState.isLoading -> {
                            LoadingScreen(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(paddingValues)
                            )
                        }
                        
                        uiState.error != null -> {
                            ErrorScreen(
                                error = uiState.error,
                                onRetry = { viewModel.loadChannels() },
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(paddingValues)
                            )
                        }
                        
                        else -> {
                            ChannelsList(
                                channels = uiState.channels,
                                onChannelClick = { channel ->
                                    playChannel(channel)
                                },
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(paddingValues)
                            )
                        }
                    }
                }
            }
        }
        
        // Load channels when activity starts
        viewModel.loadChannels()
    }
    
    private fun playChannel(channel: Channel) {
        val intent = Intent(this, VideoPlayerActivity::class.java).apply {
            putExtra("channel", channel)
        }
        startActivity(intent)
    }
}
