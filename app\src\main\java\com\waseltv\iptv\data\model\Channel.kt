package com.waseltv.iptv.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Data class representing an IPTV channel
 */
@Parcelize
data class Channel(
    val id: String,
    val name: String,
    val url: String,
    val logo: String? = null,
    val group: String? = null,
    val language: String? = null,
    val country: String? = null,
    val isLive: Boolean = true,
    val description: String? = null
) : Parcelable

/**
 * Data class representing a channel group/category
 */
@Parcelize
data class ChannelGroup(
    val name: String,
    val channels: List<Channel>
) : Parcelable

/**
 * Data class for server configuration
 */
@Parcelize
data class ServerConfig(
    val serverUrl: String,
    val username: String? = null,
    val password: String? = null,
    val playlistUrl: String? = null
) : Parcelable

/**
 * Sealed class for different types of playlist formats
 */
sealed class PlaylistType {
    object M3U : PlaylistType()
    object M3U8 : PlaylistType()
    object JSON : PlaylistType()
    object XML : PlaylistType()
}

/**
 * Data class for playlist information
 */
data class Playlist(
    val url: String,
    val type: PlaylistType,
    val channels: List<Channel> = emptyList(),
    val lastUpdated: Long = System.currentTimeMillis()
)
