package com.waseltv.iptv.data.network

import android.util.Log
import com.waseltv.iptv.data.model.Channel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.TimeUnit

/**
 * Xtream Codes IPTV API Client
 * يتعامل مع سيرفرات IPTV التي تستخدم Xtream Codes
 */
class XtreamCodesApi {
    
    companion object {
        private const val TAG = "WASEL-TV-Xtream"
        private const val SERVER_URL = "http://maventv.one:80"
        private const val USERNAME = "odaitv"
        private const val PASSWORD = "Odai2030"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    /**
     * الحصول على قائمة القنوات المباشرة من Xtream Codes API
     */
    suspend fun getLiveStreams(): List<Channel> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "جاري الحصول على قائمة القنوات المباشرة...")
            
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams"
            Log.d(TAG, "URL: $url")
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .addHeader("Accept", "application/json")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            Log.d(TAG, "Response Code: ${response.code}")
            Log.d(TAG, "Response Length: ${responseBody.length}")
            Log.d(TAG, "Response Preview: ${responseBody.take(200)}")
            
            if (!response.isSuccessful) {
                throw Exception("HTTP Error ${response.code}: $responseBody")
            }
            
            if (responseBody.isEmpty()) {
                throw Exception("Empty response from server")
            }
            
            // تحليل JSON response
            return@withContext parseChannelsFromJson(responseBody)
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على القنوات المباشرة", e)
            throw e
        }
    }
    
    /**
     * الحصول على فئات القنوات
     */
    suspend fun getLiveCategories(): List<String> = withContext(Dispatchers.IO) {
        try {
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_categories"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .addHeader("Accept", "application/json")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful && responseBody.isNotEmpty()) {
                val jsonArray = JSONArray(responseBody)
                val categories = mutableListOf<String>()
                
                for (i in 0 until jsonArray.length()) {
                    val categoryObj = jsonArray.getJSONObject(i)
                    val categoryName = categoryObj.optString("category_name", "")
                    if (categoryName.isNotEmpty()) {
                        categories.add(categoryName)
                    }
                }
                
                return@withContext categories
            }
            
            return@withContext emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على فئات القنوات", e)
            return@withContext emptyList()
        }
    }
    
    /**
     * اختبار الاتصال بالسيرفر
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "اختبار الاتصال بالسيرفر...")
            
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            Log.d(TAG, "Test Response Code: ${response.code}")
            Log.d(TAG, "Test Response: ${responseBody.take(100)}")
            
            // تحقق من نجاح الاتصال
            val isSuccess = response.isSuccessful && 
                           !responseBody.contains("Invalid Authorization") &&
                           !responseBody.contains("404 Error") &&
                           !responseBody.contains("Access Denied")
            
            Log.d(TAG, "Connection Test Result: $isSuccess")
            return@withContext isSuccess
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في اختبار الاتصال", e)
            return@withContext false
        }
    }
    
    /**
     * تحليل JSON response وتحويله إلى قائمة قنوات
     */
    private fun parseChannelsFromJson(jsonString: String): List<Channel> {
        try {
            val channels = mutableListOf<Channel>()
            val jsonArray = JSONArray(jsonString)
            
            Log.d(TAG, "عدد القنوات في JSON: ${jsonArray.length()}")
            
            for (i in 0 until jsonArray.length()) {
                val channelObj = jsonArray.getJSONObject(i)
                
                val streamId = channelObj.optString("stream_id", "")
                val name = channelObj.optString("name", "قناة غير معروفة")
                val categoryId = channelObj.optString("category_id", "")
                val streamIcon = channelObj.optString("stream_icon", "")
                val streamType = channelObj.optString("stream_type", "live")
                
                if (streamId.isNotEmpty() && streamType == "live") {
                    // بناء رابط البث حسب Xtream Codes format
                    val streamUrl = "$SERVER_URL/live/$USERNAME/$PASSWORD/$streamId.ts"
                    
                    val channel = Channel(
                        id = streamId,
                        name = name,
                        url = streamUrl,
                        logo = if (streamIcon.isNotEmpty()) streamIcon else null,
                        group = getCategoryName(categoryId),
                        language = "العربية",
                        country = "عربي",
                        description = "قناة مباشرة من Xtream Codes"
                    )
                    
                    channels.add(channel)
                    
                    if (i < 5) { // طباعة أول 5 قنوات للتشخيص
                        Log.d(TAG, "Channel $i: $name - $streamUrl")
                    }
                }
            }
            
            Log.d(TAG, "تم تحليل ${channels.size} قناة بنجاح")
            return channels
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في تحليل JSON", e)
            return emptyList()
        }
    }
    
    /**
     * تحويل category ID إلى اسم الفئة
     */
    private fun getCategoryName(categoryId: String): String {
        return when (categoryId) {
            "1" -> "أخبار"
            "2" -> "رياضة"
            "3" -> "ترفيه"
            "4" -> "أطفال"
            "5" -> "أفلام"
            "6" -> "مسلسلات"
            "7" -> "موسيقى"
            "8" -> "وثائقي"
            "9" -> "ديني"
            "10" -> "طبخ"
            else -> "عام"
        }
    }
    
    /**
     * الحصول على معلومات حساب المستخدم
     */
    suspend fun getUserInfo(): String = withContext(Dispatchers.IO) {
        try {
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful) {
                val userInfo = JSONObject(responseBody)
                val userInfoObj = userInfo.optJSONObject("user_info")
                
                return@withContext if (userInfoObj != null) {
                    val username = userInfoObj.optString("username", "غير محدد")
                    val status = userInfoObj.optString("status", "غير محدد")
                    val expDate = userInfoObj.optString("exp_date", "غير محدد")
                    val activeCons = userInfoObj.optString("active_cons", "0")
                    val maxConnections = userInfoObj.optString("max_connections", "0")
                    
                    "المستخدم: $username\nالحالة: $status\nتاريخ الانتهاء: $expDate\nالاتصالات النشطة: $activeCons/$maxConnections"
                } else {
                    "معلومات المستخدم غير متاحة"
                }
            } else {
                return@withContext "خطأ في الحصول على معلومات المستخدم: ${response.code}"
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على معلومات المستخدم", e)
            return@withContext "خطأ: ${e.message}"
        }
    }
}
