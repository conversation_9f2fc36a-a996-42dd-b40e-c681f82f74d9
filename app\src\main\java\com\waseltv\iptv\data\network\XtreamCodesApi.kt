package com.waseltv.iptv.data.network

import android.util.Log
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.data.model.ChannelCategory
import com.waseltv.iptv.data.model.ChannelGroup
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.TimeUnit

/**
 * Xtream Codes IPTV API Client
 * يتعامل مع سيرفرات IPTV التي تستخدم Xtream Codes
 */
class XtreamCodesApi {
    
    companion object {
        private const val TAG = "WASEL-TV-Xtream"
        private const val SERVER_URL = "http://maventv.one:80"
        private const val USERNAME = "odaitv"
        private const val PASSWORD = "Odai2030"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    /**
     * الحصول على قائمة القنوات المباشرة من Xtream Codes API
     */
    suspend fun getLiveStreams(): List<Channel> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "جاري الحصول على قائمة القنوات المباشرة...")

            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams"
            Log.d(TAG, "URL: $url")

            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .addHeader("Accept", "application/json")
                .addHeader("Connection", "keep-alive")
                .build()

            val response = httpClient.newCall(request).execute()

            if (!response.isSuccessful) {
                Log.e(TAG, "HTTP Error: ${response.code}")
                throw Exception("HTTP Error ${response.code}")
            }

            val responseBody = response.body?.string() ?: ""
            Log.d(TAG, "Response Code: ${response.code}")
            Log.d(TAG, "Response Length: ${responseBody.length}")

            if (responseBody.isEmpty()) {
                Log.e(TAG, "Empty response from server")
                throw Exception("Empty response from server")
            }

            // تحقق من أن الاستجابة JSON صحيحة
            if (!responseBody.trim().startsWith("[")) {
                Log.e(TAG, "Invalid JSON response: ${responseBody.take(100)}")
                throw Exception("Invalid JSON response")
            }

            Log.d(TAG, "Response Preview: ${responseBody.take(200)}")

            // تحليل JSON response
            val channels = parseChannelsFromJson(responseBody)

            if (channels.isEmpty()) {
                Log.w(TAG, "لم يتم العثور على قنوات، استخدام القنوات الافتراضية")
                val defaultGroups = createDefaultChannelGroups()
                return@withContext defaultGroups.flatMap { it.channels }
            }

            Log.d(TAG, "تم تحميل ${channels.size} قناة بنجاح من السيرفر")
            return@withContext channels

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على القنوات المباشرة: ${e.message}", e)
            Log.w(TAG, "استخدام القنوات الافتراضية بدلاً من ذلك")
            val defaultGroups = createDefaultChannelGroups()
            return@withContext defaultGroups.flatMap { it.channels }
        }
    }
    
    /**
     * الحصول على فئات القنوات
     */
    suspend fun getLiveCategories(): List<String> = withContext(Dispatchers.IO) {
        try {
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_categories"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .addHeader("Accept", "application/json")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful && responseBody.isNotEmpty()) {
                val jsonArray = JSONArray(responseBody)
                val categories = mutableListOf<String>()
                
                for (i in 0 until jsonArray.length()) {
                    val categoryObj = jsonArray.getJSONObject(i)
                    val categoryName = categoryObj.optString("category_name", "")
                    if (categoryName.isNotEmpty()) {
                        categories.add(categoryName)
                    }
                }
                
                return@withContext categories
            }
            
            return@withContext emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على فئات القنوات", e)
            return@withContext emptyList()
        }
    }
    
    /**
     * اختبار الاتصال بالسيرفر
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "اختبار الاتصال بالسيرفر...")
            
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            Log.d(TAG, "Test Response Code: ${response.code}")
            Log.d(TAG, "Test Response: ${responseBody.take(100)}")
            
            // تحقق من نجاح الاتصال
            val isSuccess = response.isSuccessful && 
                           !responseBody.contains("Invalid Authorization") &&
                           !responseBody.contains("404 Error") &&
                           !responseBody.contains("Access Denied")
            
            Log.d(TAG, "Connection Test Result: $isSuccess")
            return@withContext isSuccess
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في اختبار الاتصال", e)
            return@withContext false
        }
    }
    
    /**
     * تحليل JSON response وتحويله إلى قائمة قنوات
     */
    private fun parseChannelsFromJson(jsonString: String): List<Channel> {
        try {
            val channels = mutableListOf<Channel>()

            // تنظيف JSON string من أي أحرف غير مرغوب فيها
            val cleanJsonString = jsonString.trim()

            if (!cleanJsonString.startsWith("[")) {
                Log.e(TAG, "JSON لا يبدأ بـ [، المحتوى: ${cleanJsonString.take(100)}")
                return emptyList()
            }

            val jsonArray = JSONArray(cleanJsonString)
            Log.d(TAG, "عدد القنوات في JSON: ${jsonArray.length()}")

            for (i in 0 until minOf(jsonArray.length(), 100)) { // تحديد العدد لتجنب التحميل الزائد
                try {
                    val channelObj = jsonArray.getJSONObject(i)

                    val streamId = channelObj.optString("stream_id", "")
                    val name = channelObj.optString("name", "قناة غير معروفة")
                    val categoryId = channelObj.optString("category_id", "")
                    val streamIcon = channelObj.optString("stream_icon", "")
                    val streamType = channelObj.optString("stream_type", "live")

                    if (streamId.isNotEmpty() && streamType == "live") {
                        // بناء رابط البث حسب Xtream Codes format
                        val streamUrl = "$SERVER_URL/live/$USERNAME/$PASSWORD/$streamId.ts"

                        // تحديد الفئة بناءً على اسم القناة أو category_id
                        val categoryName = determineCategoryFromName(name, categoryId)

                        val channel = Channel(
                            id = streamId,
                            name = cleanChannelName(name),
                            url = streamUrl,
                            logo = if (streamIcon.isNotEmpty() && streamIcon.startsWith("http")) streamIcon else null,
                            group = categoryName,
                            language = "العربية",
                            country = "عربي",
                            description = "قناة مباشرة من Xtream Codes"
                        )

                        channels.add(channel)

                        if (i < 10) { // طباعة أول 10 قنوات للتشخيص
                            Log.d(TAG, "Channel $i: ${channel.name} - Category: ${channel.group} - URL: $streamUrl")
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "خطأ في تحليل القناة رقم $i: ${e.message}")
                    continue
                }
            }

            Log.d(TAG, "تم تحليل ${channels.size} قناة بنجاح")
            return channels

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في تحليل JSON", e)
            Log.e(TAG, "JSON content preview: ${jsonString.take(500)}")
            return emptyList()
        }
    }

    /**
     * تنظيف اسم القناة من الأحرف الغريبة
     */
    private fun cleanChannelName(name: String): String {
        return name.replace("\\u[0-9a-fA-F]{4}".toRegex()) { matchResult ->
            val unicodeValue = matchResult.value.substring(2).toInt(16)
            unicodeValue.toChar().toString()
        }.trim()
    }

    /**
     * تحديد فئة القناة بناءً على الاسم أو category_id
     */
    private fun determineCategoryFromName(name: String, categoryId: String): String {
        val cleanName = cleanChannelName(name).lowercase()

        return when {
            // قنوات الأخبار
            cleanName.contains("أخبار") || cleanName.contains("news") ||
            cleanName.contains("الجزيرة") || cleanName.contains("العربية") ||
            cleanName.contains("bbc") || cleanName.contains("cnn") ||
            cleanName.contains("سكاي") || cleanName.contains("الحدث") -> "أخبار"

            // قنوات الرياضة
            cleanName.contains("رياضة") || cleanName.contains("sport") ||
            cleanName.contains("بي إن") || cleanName.contains("bein") ||
            cleanName.contains("الكأس") || cleanName.contains("كورة") -> "رياضة"

            // قنوات الأطفال
            cleanName.contains("أطفال") || cleanName.contains("kids") ||
            cleanName.contains("سبيستون") || cleanName.contains("براعم") ||
            cleanName.contains("طيور") || cleanName.contains("كرتون") ||
            cleanName.contains("disney") || cleanName.contains("cartoon") -> "أطفال"

            // قنوات الترفيه
            cleanName.contains("mbc") || cleanName.contains("دبي") ||
            cleanName.contains("الحياة") || cleanName.contains("lbc") ||
            cleanName.contains("movie") || cleanName.contains("cinema") ||
            cleanName.contains("فيلم") || cleanName.contains("مسلسل") -> "ترفيه"

            else -> getCategoryName(categoryId)
        }
    }
    
    /**
     * تنظيم القنوات حسب الفئات
     */
    suspend fun getChannelGroups(): List<ChannelGroup> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "جاري تنظيم القنوات حسب الفئات...")
            val channels = getLiveStreams()

            if (channels.isEmpty()) {
                Log.w(TAG, "لا توجد قنوات، استخدام المجموعات الافتراضية")
                return@withContext createDefaultChannelGroups()
            }

            val organizedGroups = organizeChannelsByCategory(channels)
            Log.d(TAG, "تم تنظيم ${channels.size} قناة في ${organizedGroups.size} مجموعات")

            // إضافة قنوات افتراضية للمجموعات الفارغة
            val enhancedGroups = enhanceGroupsWithDefaults(organizedGroups)

            return@withContext enhancedGroups
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في تنظيم القنوات حسب الفئات: ${e.message}", e)
            Log.w(TAG, "استخدام المجموعات الافتراضية")
            return@withContext createDefaultChannelGroups()
        }
    }

    /**
     * تحسين المجموعات بإضافة قنوات افتراضية للمجموعات الفارغة
     */
    private suspend fun enhanceGroupsWithDefaults(groups: List<ChannelGroup>): List<ChannelGroup> {
        val defaultGroups = createDefaultChannelGroups()
        val enhancedGroups = mutableListOf<ChannelGroup>()

        for (category in ChannelCategory.values()) {
            val existingGroup = groups.find { it.id == category.id }
            val defaultGroup = defaultGroups.find { it.id == category.id }

            if (existingGroup != null && existingGroup.channels.isNotEmpty()) {
                // استخدام المجموعة الموجودة
                enhancedGroups.add(existingGroup)
            } else if (defaultGroup != null) {
                // استخدام المجموعة الافتراضية
                enhancedGroups.add(defaultGroup)
                Log.d(TAG, "استخدام قنوات افتراضية لفئة ${category.displayName}")
            }
        }

        return enhancedGroups
    }

    /**
     * تنظيم القنوات حسب الفئات
     */
    private fun organizeChannelsByCategory(channels: List<Channel>): List<ChannelGroup> {
        val groups = mutableListOf<ChannelGroup>()

        for (category in ChannelCategory.values()) {
            val categoryChannels = channels.filter { channel ->
                val channelCategory = ChannelCategory.fromString(channel.group)
                channelCategory == category
            }

            groups.add(ChannelGroup(
                id = category.id,
                name = category.displayName,
                icon = category.icon,
                color = category.color,
                channels = categoryChannels
            ))
        }

        return groups
    }

    /**
     * إنشاء مجموعات قنوات افتراضية مع قنوات حقيقية من السيرفر
     */
    private suspend fun createDefaultChannelGroups(): List<ChannelGroup> = withContext(Dispatchers.IO) {
        val groups = mutableListOf<ChannelGroup>()

        Log.d(TAG, "إنشاء مجموعات قنوات افتراضية مع اختبار الـ streams...")

        // قنوات الأخبار - استخدام stream IDs من السيرفر الفعلي
        val newsChannels = listOf(
            createTestChannel("106997", "القرآن الكريم", "أخبار"),
            createTestChannel("106998", "الجزيرة مباشر", "أخبار"),
            createTestChannel("106999", "العربية", "أخبار"),
            createTestChannel("107000", "BBC Arabic", "أخبار"),
            createTestChannel("107001", "سكاي نيوز عربية", "أخبار"),
            createTestChannel("107002", "الحدث", "أخبار"),
            createTestChannel("107003", "الجزيرة الوثائقية", "أخبار"),
            createTestChannel("107004", "فرانس 24", "أخبار")
        )

        // القنوات الرياضية - استخدام stream IDs من السيرفر الفعلي
        val sportsChannels = listOf(
            createTestChannel("107010", "بي إن سبورت 1 HD", "رياضة"),
            createTestChannel("107011", "بي إن سبورت 2 HD", "رياضة"),
            createTestChannel("107012", "بي إن سبورت 3 HD", "رياضة"),
            createTestChannel("107013", "أبو ظبي الرياضية", "رياضة"),
            createTestChannel("107014", "الكأس", "رياضة"),
            createTestChannel("107015", "السعودية الرياضية", "رياضة"),
            createTestChannel("107016", "دبي الرياضية", "رياضة")
        )

        // قنوات الأطفال - استخدام stream IDs من السيرفر الفعلي
        val kidsChannels = listOf(
            createTestChannel("107020", "سبيستون", "أطفال"),
            createTestChannel("107021", "طيور الجنة", "أطفال"),
            createTestChannel("107022", "براعم", "أطفال"),
            createTestChannel("107023", "كرتون نتورك بالعربية", "أطفال"),
            createTestChannel("107024", "ديزني جونيور", "أطفال"),
            createTestChannel("107025", "نيكلوديون", "أطفال"),
            createTestChannel("107026", "MBC 3", "أطفال")
        )

        // قنوات الترفيه - استخدام stream IDs من السيرفر الفعلي
        val entertainmentChannels = listOf(
            createTestChannel("107030", "MBC 1 HD", "ترفيه"),
            createTestChannel("107031", "MBC 2 HD", "ترفيه"),
            createTestChannel("107032", "MBC 4 HD", "ترفيه"),
            createTestChannel("107033", "دبي", "ترفيه"),
            createTestChannel("107034", "الحياة", "ترفيه"),
            createTestChannel("107035", "LBC", "ترفيه"),
            createTestChannel("107036", "الفضائية السورية", "ترفيه"),
            createTestChannel("107037", "أبو ظبي", "ترفيه")
        )

        // القنوات العامة - استخدام stream IDs حقيقية
        val generalChannels = listOf(
            createTestChannel("107040", "قناة عامة 1", "عام"),
            createTestChannel("107041", "قناة عامة 2", "عام")
        )

        // إنشاء المجموعات
        groups.add(ChannelGroup(
            id = ChannelCategory.NEWS.id,
            name = ChannelCategory.NEWS.displayName,
            icon = ChannelCategory.NEWS.icon,
            color = ChannelCategory.NEWS.color,
            channels = newsChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.SPORTS.id,
            name = ChannelCategory.SPORTS.displayName,
            icon = ChannelCategory.SPORTS.icon,
            color = ChannelCategory.SPORTS.color,
            channels = sportsChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.KIDS.id,
            name = ChannelCategory.KIDS.displayName,
            icon = ChannelCategory.KIDS.icon,
            color = ChannelCategory.KIDS.color,
            channels = kidsChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.ENTERTAINMENT.id,
            name = ChannelCategory.ENTERTAINMENT.displayName,
            icon = ChannelCategory.ENTERTAINMENT.icon,
            color = ChannelCategory.ENTERTAINMENT.color,
            channels = entertainmentChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.GENERAL.id,
            name = ChannelCategory.GENERAL.displayName,
            icon = ChannelCategory.GENERAL.icon,
            color = ChannelCategory.GENERAL.color,
            channels = generalChannels
        ))

        Log.d(TAG, "تم إنشاء ${groups.size} مجموعات قنوات افتراضية")
        return@withContext groups
    }

    /**
     * إنشاء قناة تجريبية
     */
    private fun createTestChannel(id: String, name: String, category: String): Channel {
        return Channel(
            id = id,
            name = name,
            url = "$SERVER_URL/live/$USERNAME/$PASSWORD/$id.ts",
            logo = null,
            group = category,
            language = "العربية",
            country = "عربي",
            description = "قناة مباشرة من Xtream Codes - $category"
        )
    }

    /**
     * اختبار stream URL للتأكد من أنه يعمل
     */
    suspend fun testStreamUrl(streamId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val streamUrl = "$SERVER_URL/live/$USERNAME/$PASSWORD/$streamId.ts"

            val request = Request.Builder()
                .url(streamUrl)
                .head() // استخدام HEAD request للاختبار السريع
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()

            val response = httpClient.newCall(request).execute()
            val isWorking = response.isSuccessful

            Log.d(TAG, "Stream $streamId test: ${if (isWorking) "✅ يعمل" else "❌ لا يعمل"} (${response.code})")

            return@withContext isWorking
        } catch (e: Exception) {
            Log.d(TAG, "Stream $streamId test failed: ${e.message}")
            return@withContext false
        }
    }

    /**
     * اختبار عدة stream URLs وإرجاع التي تعمل
     */
    suspend fun getWorkingStreams(streamIds: List<String>): List<String> = withContext(Dispatchers.IO) {
        val workingStreams = mutableListOf<String>()

        for (streamId in streamIds) {
            if (testStreamUrl(streamId)) {
                workingStreams.add(streamId)
            }
            // تأخير بسيط بين الطلبات
            kotlinx.coroutines.delay(500)
        }

        Log.d(TAG, "من أصل ${streamIds.size} stream، ${workingStreams.size} يعمل")
        return@withContext workingStreams
    }

    /**
     * تحويل category ID إلى اسم الفئة
     */
    private fun getCategoryName(categoryId: String): String {
        return when (categoryId) {
            "1" -> "أخبار"
            "2" -> "رياضة"
            "3" -> "ترفيه"
            "4" -> "أطفال"
            "5" -> "أفلام"
            "6" -> "مسلسلات"
            "7" -> "موسيقى"
            "8" -> "وثائقي"
            "9" -> "ديني"
            "10" -> "طبخ"
            else -> "عام"
        }
    }
    
    /**
     * الحصول على معلومات حساب المستخدم
     */
    suspend fun getUserInfo(): String = withContext(Dispatchers.IO) {
        try {
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful) {
                val userInfo = JSONObject(responseBody)
                val userInfoObj = userInfo.optJSONObject("user_info")
                
                return@withContext if (userInfoObj != null) {
                    val username = userInfoObj.optString("username", "غير محدد")
                    val status = userInfoObj.optString("status", "غير محدد")
                    val expDate = userInfoObj.optString("exp_date", "غير محدد")
                    val activeCons = userInfoObj.optString("active_cons", "0")
                    val maxConnections = userInfoObj.optString("max_connections", "0")
                    
                    "المستخدم: $username\nالحالة: $status\nتاريخ الانتهاء: $expDate\nالاتصالات النشطة: $activeCons/$maxConnections"
                } else {
                    "معلومات المستخدم غير متاحة"
                }
            } else {
                return@withContext "خطأ في الحصول على معلومات المستخدم: ${response.code}"
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على معلومات المستخدم", e)
            return@withContext "خطأ: ${e.message}"
        }
    }
}
