package com.waseltv.iptv.data.network

import android.util.Log
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.data.model.ChannelCategory
import com.waseltv.iptv.data.model.ChannelGroup
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.TimeUnit

/**
 * Xtream Codes IPTV API Client
 * يتعامل مع سيرفرات IPTV التي تستخدم Xtream Codes
 */
class XtreamCodesApi {
    
    companion object {
        private const val TAG = "WASEL-TV-Xtream"
        private const val SERVER_URL = "http://maventv.one:80"
        private const val USERNAME = "odaitv"
        private const val PASSWORD = "Odai2030"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    /**
     * الحصول على قائمة القنوات المباشرة من Xtream Codes API
     */
    suspend fun getLiveStreams(): List<Channel> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "جاري الحصول على قائمة القنوات المباشرة...")
            
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams"
            Log.d(TAG, "URL: $url")
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .addHeader("Accept", "application/json")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            Log.d(TAG, "Response Code: ${response.code}")
            Log.d(TAG, "Response Length: ${responseBody.length}")
            Log.d(TAG, "Response Preview: ${responseBody.take(200)}")
            
            if (!response.isSuccessful) {
                throw Exception("HTTP Error ${response.code}: $responseBody")
            }
            
            if (responseBody.isEmpty()) {
                throw Exception("Empty response from server")
            }
            
            // تحليل JSON response
            return@withContext parseChannelsFromJson(responseBody)
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على القنوات المباشرة", e)
            throw e
        }
    }
    
    /**
     * الحصول على فئات القنوات
     */
    suspend fun getLiveCategories(): List<String> = withContext(Dispatchers.IO) {
        try {
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_categories"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .addHeader("Accept", "application/json")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful && responseBody.isNotEmpty()) {
                val jsonArray = JSONArray(responseBody)
                val categories = mutableListOf<String>()
                
                for (i in 0 until jsonArray.length()) {
                    val categoryObj = jsonArray.getJSONObject(i)
                    val categoryName = categoryObj.optString("category_name", "")
                    if (categoryName.isNotEmpty()) {
                        categories.add(categoryName)
                    }
                }
                
                return@withContext categories
            }
            
            return@withContext emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على فئات القنوات", e)
            return@withContext emptyList()
        }
    }
    
    /**
     * اختبار الاتصال بالسيرفر
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "اختبار الاتصال بالسيرفر...")
            
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            Log.d(TAG, "Test Response Code: ${response.code}")
            Log.d(TAG, "Test Response: ${responseBody.take(100)}")
            
            // تحقق من نجاح الاتصال
            val isSuccess = response.isSuccessful && 
                           !responseBody.contains("Invalid Authorization") &&
                           !responseBody.contains("404 Error") &&
                           !responseBody.contains("Access Denied")
            
            Log.d(TAG, "Connection Test Result: $isSuccess")
            return@withContext isSuccess
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في اختبار الاتصال", e)
            return@withContext false
        }
    }
    
    /**
     * تحليل JSON response وتحويله إلى قائمة قنوات
     */
    private fun parseChannelsFromJson(jsonString: String): List<Channel> {
        try {
            val channels = mutableListOf<Channel>()
            val jsonArray = JSONArray(jsonString)
            
            Log.d(TAG, "عدد القنوات في JSON: ${jsonArray.length()}")
            
            for (i in 0 until jsonArray.length()) {
                val channelObj = jsonArray.getJSONObject(i)
                
                val streamId = channelObj.optString("stream_id", "")
                val name = channelObj.optString("name", "قناة غير معروفة")
                val categoryId = channelObj.optString("category_id", "")
                val streamIcon = channelObj.optString("stream_icon", "")
                val streamType = channelObj.optString("stream_type", "live")
                
                if (streamId.isNotEmpty() && streamType == "live") {
                    // بناء رابط البث حسب Xtream Codes format
                    val streamUrl = "$SERVER_URL/live/$USERNAME/$PASSWORD/$streamId.ts"
                    
                    val channel = Channel(
                        id = streamId,
                        name = name,
                        url = streamUrl,
                        logo = if (streamIcon.isNotEmpty()) streamIcon else null,
                        group = getCategoryName(categoryId),
                        language = "العربية",
                        country = "عربي",
                        description = "قناة مباشرة من Xtream Codes"
                    )
                    
                    channels.add(channel)
                    
                    if (i < 5) { // طباعة أول 5 قنوات للتشخيص
                        Log.d(TAG, "Channel $i: $name - $streamUrl")
                    }
                }
            }
            
            Log.d(TAG, "تم تحليل ${channels.size} قناة بنجاح")
            return channels
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في تحليل JSON", e)
            return emptyList()
        }
    }
    
    /**
     * تنظيم القنوات حسب الفئات
     */
    suspend fun getChannelGroups(): List<ChannelGroup> = withContext(Dispatchers.IO) {
        try {
            val channels = getLiveStreams()
            return@withContext organizeChannelsByCategory(channels)
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في تنظيم القنوات حسب الفئات", e)
            return@withContext createDefaultChannelGroups()
        }
    }

    /**
     * تنظيم القنوات حسب الفئات
     */
    private fun organizeChannelsByCategory(channels: List<Channel>): List<ChannelGroup> {
        val groups = mutableListOf<ChannelGroup>()

        for (category in ChannelCategory.values()) {
            val categoryChannels = channels.filter { channel ->
                val channelCategory = ChannelCategory.fromString(channel.group)
                channelCategory == category
            }

            groups.add(ChannelGroup(
                id = category.id,
                name = category.displayName,
                icon = category.icon,
                color = category.color,
                channels = categoryChannels
            ))
        }

        return groups
    }

    /**
     * إنشاء مجموعات قنوات افتراضية مع قنوات تجريبية
     */
    private fun createDefaultChannelGroups(): List<ChannelGroup> {
        val groups = mutableListOf<ChannelGroup>()

        // قنوات الأخبار
        val newsChannels = listOf(
            createTestChannel("1", "الجزيرة", "أخبار"),
            createTestChannel("2", "العربية", "أخبار"),
            createTestChannel("3", "BBC Arabic", "أخبار"),
            createTestChannel("4", "سكاي نيوز عربية", "أخبار"),
            createTestChannel("5", "الحدث", "أخبار")
        )

        // القنوات الرياضية
        val sportsChannels = listOf(
            createTestChannel("10", "بي إن سبورت 1", "رياضة"),
            createTestChannel("11", "بي إن سبورت 2", "رياضة"),
            createTestChannel("12", "أبو ظبي الرياضية", "رياضة"),
            createTestChannel("13", "الكأس", "رياضة"),
            createTestChannel("14", "السعودية الرياضية", "رياضة")
        )

        // قنوات الأطفال
        val kidsChannels = listOf(
            createTestChannel("20", "سبيستون", "أطفال"),
            createTestChannel("21", "طيور الجنة", "أطفال"),
            createTestChannel("22", "براعم", "أطفال"),
            createTestChannel("23", "كرتون نتورك", "أطفال"),
            createTestChannel("24", "ديزني", "أطفال")
        )

        // قنوات الترفيه
        val entertainmentChannels = listOf(
            createTestChannel("30", "MBC 1", "ترفيه"),
            createTestChannel("31", "MBC 2", "ترفيه"),
            createTestChannel("32", "MBC 4", "ترفيه"),
            createTestChannel("33", "دبي", "ترفيه"),
            createTestChannel("34", "الحياة", "ترفيه")
        )

        // القنوات العامة
        val generalChannels = listOf(
            createTestChannel("40", "قناة عامة 1", "عام"),
            createTestChannel("41", "قناة عامة 2", "عام")
        )

        // إنشاء المجموعات
        groups.add(ChannelGroup(
            id = ChannelCategory.NEWS.id,
            name = ChannelCategory.NEWS.displayName,
            icon = ChannelCategory.NEWS.icon,
            color = ChannelCategory.NEWS.color,
            channels = newsChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.SPORTS.id,
            name = ChannelCategory.SPORTS.displayName,
            icon = ChannelCategory.SPORTS.icon,
            color = ChannelCategory.SPORTS.color,
            channels = sportsChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.KIDS.id,
            name = ChannelCategory.KIDS.displayName,
            icon = ChannelCategory.KIDS.icon,
            color = ChannelCategory.KIDS.color,
            channels = kidsChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.ENTERTAINMENT.id,
            name = ChannelCategory.ENTERTAINMENT.displayName,
            icon = ChannelCategory.ENTERTAINMENT.icon,
            color = ChannelCategory.ENTERTAINMENT.color,
            channels = entertainmentChannels
        ))

        groups.add(ChannelGroup(
            id = ChannelCategory.GENERAL.id,
            name = ChannelCategory.GENERAL.displayName,
            icon = ChannelCategory.GENERAL.icon,
            color = ChannelCategory.GENERAL.color,
            channels = generalChannels
        ))

        return groups
    }

    /**
     * إنشاء قناة تجريبية
     */
    private fun createTestChannel(id: String, name: String, category: String): Channel {
        return Channel(
            id = id,
            name = name,
            url = "$SERVER_URL/live/$USERNAME/$PASSWORD/$id.ts",
            logo = null,
            group = category,
            language = "العربية",
            country = "عربي",
            description = "قناة تجريبية - $category"
        )
    }

    /**
     * تحويل category ID إلى اسم الفئة
     */
    private fun getCategoryName(categoryId: String): String {
        return when (categoryId) {
            "1" -> "أخبار"
            "2" -> "رياضة"
            "3" -> "ترفيه"
            "4" -> "أطفال"
            "5" -> "أفلام"
            "6" -> "مسلسلات"
            "7" -> "موسيقى"
            "8" -> "وثائقي"
            "9" -> "ديني"
            "10" -> "طبخ"
            else -> "عام"
        }
    }
    
    /**
     * الحصول على معلومات حساب المستخدم
     */
    suspend fun getUserInfo(): String = withContext(Dispatchers.IO) {
        try {
            val url = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD"
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful) {
                val userInfo = JSONObject(responseBody)
                val userInfoObj = userInfo.optJSONObject("user_info")
                
                return@withContext if (userInfoObj != null) {
                    val username = userInfoObj.optString("username", "غير محدد")
                    val status = userInfoObj.optString("status", "غير محدد")
                    val expDate = userInfoObj.optString("exp_date", "غير محدد")
                    val activeCons = userInfoObj.optString("active_cons", "0")
                    val maxConnections = userInfoObj.optString("max_connections", "0")
                    
                    "المستخدم: $username\nالحالة: $status\nتاريخ الانتهاء: $expDate\nالاتصالات النشطة: $activeCons/$maxConnections"
                } else {
                    "معلومات المستخدم غير متاحة"
                }
            } else {
                return@withContext "خطأ في الحصول على معلومات المستخدم: ${response.code}"
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الحصول على معلومات المستخدم", e)
            return@withContext "خطأ: ${e.message}"
        }
    }
}
