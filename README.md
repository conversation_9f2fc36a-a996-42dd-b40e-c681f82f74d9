# WASEL-TV واصل تي في

تطبيق أندرويد لتشغيل قنوات IPTV المباشرة

## المميزات

- ✅ تشغيل قنوات IPTV المباشرة
- ✅ دعم تنسيقات M3U و M3U8
- ✅ واجهة مستخدم عربية بسيطة وجميلة
- ✅ ألوان أبيض وأزرق أنيقة
- ✅ دعم أندرويد 7.0 فما فوق
- ✅ مشغل فيديو متقدم باستخدام ExoPlayer
- ✅ إعدادات لحفظ روابط السيرفرات
- ✅ تصميم متجاوب وحديث

## المتطلبات

- Android 7.0 (API level 24) أو أحدث
- اتصال بالإنترنت
- رابط سيرفر IPTV صالح

## التثبيت

1. قم بتحميل ملف APK من صفحة الإصدارات
2. فعّل "مصادر غير معروفة" في إعدادات الأمان
3. قم بتثبيت التطبيق
4. افتح التطبيق وأدخل إعدادات السيرفر

## الاستخدام

### إعداد السيرفر

1. اضغط على أيقونة الإعدادات في الشاشة الرئيسية
2. أدخل رابط السيرفر أو رابط قائمة M3U
3. أدخل اسم المستخدم وكلمة المرور (إذا كانت مطلوبة)
4. اضغط على "حفظ"

### مشاهدة القنوات

1. ستظهر قائمة القنوات في الشاشة الرئيسية
2. اضغط على أي قناة لبدء التشغيل
3. استخدم عناصر التحكم في المشغل للتحكم في التشغيل

## التقنيات المستخدمة

- **Kotlin** - لغة البرمجة الأساسية
- **Jetpack Compose** - لبناء واجهة المستخدم
- **ExoPlayer** - لتشغيل الفيديو والصوت
- **Material Design 3** - للتصميم والألوان
- **DataStore** - لحفظ الإعدادات
- **Coroutines** - للعمليات غير المتزامنة
- **OkHttp** - لطلبات الشبكة

## هيكل المشروع

```
app/
├── src/main/java/com/waseltv/iptv/
│   ├── data/
│   │   ├── model/          # نماذج البيانات
│   │   ├── network/        # طبقة الشبكة وتحليل M3U
│   │   ├── preferences/    # إدارة الإعدادات
│   │   └── repository/     # طبقة البيانات
│   ├── ui/
│   │   ├── components/     # مكونات واجهة المستخدم
│   │   ├── player/         # مشغل الفيديو
│   │   ├── settings/       # شاشة الإعدادات
│   │   ├── theme/          # ألوان وتصميم التطبيق
│   │   └── viewmodel/      # ViewModels
│   └── MainActivity.kt     # النشاط الرئيسي
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:

- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## إخلاء المسؤولية

هذا التطبيق مخصص للاستخدام مع محتوى IPTV قانوني فقط. المطورون غير مسؤولين عن أي استخدام غير قانوني للتطبيق.
