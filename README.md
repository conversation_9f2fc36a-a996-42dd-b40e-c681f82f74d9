# WASEL-TV واصل تي في

تطبيق أندرويد لتشغيل قنوات IPTV المباشرة باستخدام **Xtream Codes API**

## المميزات

- ✅ **Xtream Codes IPTV** - يستخدم API متقدم حصرياً
- ✅ **تنظيم القنوات بالفئات** - أخبار، رياضة، أطفال، ترفيه
- ✅ **أيقونات احترافية** - تعبر عن محتوى كل فئة
- ✅ تشغيل قنوات IPTV المباشرة عالية الجودة
- ✅ **متصل بسيرفر ثابت** - لا حاجة لإعدادات معقدة
- ✅ **سيرفر maventv.one:80** مدمج مسبقاً
- ✅ واجهة مستخدم عربية بسيطة وجميلة
- ✅ **تصميم حديث** مع ألوان مميزة لكل فئة
- ✅ دعم أندرويد 7.0 فما فوق
- ✅ مشغل فيديو متقدم باستخدام ExoPlayer
- ✅ تشخيص متقدم وعرض معلومات المستخدم

## المتطلبات

- Android 7.0 (API level 24) أو أحدث
- اتصال بالإنترنت
- **لا حاجة لسيرفر خارجي** - السيرفر مدمج مسبقاً

## التثبيت

1. قم بتحميل ملف APK من صفحة الإصدارات
2. فعّل "مصادر غير معروفة" في إعدادات الأمان
3. قم بتثبيت التطبيق
4. افتح التطبيق وأدخل إعدادات السيرفر

## الاستخدام

### البدء السريع

1. قم بتثبيت التطبيق
2. افتح التطبيق - **سيتصل تلقائياً بالسيرفر**
3. ستظهر قائمة القنوات فوراً
4. اضغط على أي قناة لبدء المشاهدة

### معلومات Xtream Codes المدمج

- **النوع:** Xtream Codes IPTV API
- **السيرفر:** http://maventv.one:80
- **المستخدم:** odaitv
- **كلمة المرور:** Odai2030
- **الحالة:** متصل تلقائياً

### كيفية عمل البثوث المباشرة

التطبيق يستخدم **Xtream Codes API** لتشغيل القنوات المباشرة:

1. **تحميل قائمة القنوات:**
   ```
   GET http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030&action=get_live_streams
   ```

2. **تشغيل القناة:**
   ```
   http://maventv.one:80/live/odaitv/Odai2030/[STREAM_ID].ts
   ```

3. **أمثلة على Stream URLs حقيقية:**
   - القرآن الكريم: `http://maventv.one:80/live/odaitv/Odai2030/106997.ts`
   - الجزيرة: `http://maventv.one:80/live/odaitv/Odai2030/106998.ts`
   - بي إن سبورت 1: `http://maventv.one:80/live/odaitv/Odai2030/107010.ts`

4. **التطبيق يقوم بـ:**
   - تنظيم القنوات حسب الفئات تلقائياً
   - اختبار صحة الـ streams قبل عرضها
   - استخدام قنوات افتراضية في حالة فشل الاتصال
   - معالجة أخطاء ExoPlayer مع رسائل مفصلة
   - توفير أدوات تشخيص متقدمة لاختبار الـ streams

### الأقسام الرئيسية (4 أقسام فقط)

التطبيق يعرض **4 أقسام رئيسية فقط** كما طُلب:

1. **📰 أخبار** - قنوات الأخبار والتقارير الإخبارية
2. **⚽ رياضة** - القنوات الرياضية والمباريات المباشرة
3. **🧸 أطفال** - قنوات الأطفال والكرتون
4. **🎬 ترفيه** - قنوات الترفيه والأفلام والمسلسلات

**عند الضغط على أي قسم:** يتم عرض جميع القنوات التي تخص ذلك القسم

### مشاهدة القنوات

1. اختر الفئة المطلوبة من الشريط العلوي
2. ستظهر قائمة القنوات الخاصة بتلك الفئة مع Stream IDs حقيقية
3. اضغط على أي قناة لبدء التشغيل المباشر
4. استخدم عناصر التحكم في المشغل للتحكم في التشغيل
5. جميع القنوات تستخدم Xtream Codes format: `/live/username/password/streamid.ts`

## 🔧 استكشاف الأخطاء

### إذا لم تعمل القنوات:

1. **تحقق من الاتصال بالإنترنت**
   - تأكد من وجود اتصال إنترنت مستقر
   - جرب فتح موقع ويب في المتصفح

2. **استخدم أدوات التشخيص**
   - اضغط على أيقونة 🔍 في الشاشة الرئيسية
   - اختبر الـ streams المختلفة لمعرفة أيها يعمل
   - راجع رسائل الخطأ التفصيلية

3. **تحقق من حالة السيرفر**
   - السيرفر: `maventv.one:80`
   - يمكن اختبار الاتصال من المتصفح: `http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030`

4. **الحلول المطبقة لمشكلة "تنسيق غير مدعوم":**
   - ✅ **تجريب تنسيقات متعددة** - (.ts, .m3u8, .mp4, مسارات بديلة)
   - ✅ **اختبار URL قبل التشغيل** - التحقق من صحة الرابط أولاً
   - ✅ **User-Agent محسن** - استخدام VLC User-Agent للتوافق
   - ✅ **Headers مخصصة** - إضافة headers مطلوبة لـ Xtream Codes
   - ✅ **Buffer settings محسنة** - تحسين إعدادات التخزين المؤقت للـ live streams
   - ✅ **قنوات تجريبية مضمونة** - قنوات تعمل بالتأكيد للاختبار
   - ✅ **رسائل خطأ مفصلة** - تشخيص دقيق للمشاكل

5. **رسائل الخطأ الشائعة:**
   - **"فشل الاتصال بالشبكة"** → تحقق من الإنترنت
   - **"انتهت مهلة الاتصال"** → السيرفر بطيء، جرب مرة أخرى
   - **"خطأ في السيرفر"** → المشكلة من جانب السيرفر
   - **"تنسيق الفيديو غير مدعوم"** → الـ stream تالف أو غير متاح

6. **أدوات الاختبار المتاحة:**
   - 🔍 **أدوات التشخيص في التطبيق** - اضغط على أيقونة 🔍
   - 🔧 **اختبار تنسيقات Stream** - `test_formats.html` (جديد!)
   - 🌐 **اختبار Stream مباشر** - `test_stream_direct.html`
   - 📡 **اختبار الاتصال** - `test_connection.html`
   - 📱 **اختبار الأقسام** - `test_categories.html`

7. **نصائح للحصول على أفضل أداء:**
   - استخدم اتصال WiFi مستقر
   - أغلق التطبيقات الأخرى التي تستهلك الإنترنت
   - جرب قنوات مختلفة إذا لم تعمل قناة معينة
   - انتظر اكتمال اختبار الرابط قبل التشغيل

## التقنيات المستخدمة

- **Kotlin** - لغة البرمجة الأساسية
- **Jetpack Compose** - لبناء واجهة المستخدم
- **ExoPlayer** - لتشغيل الفيديو والصوت
- **Material Design 3** - للتصميم والألوان
- **DataStore** - لحفظ الإعدادات
- **Coroutines** - للعمليات غير المتزامنة
- **OkHttp** - لطلبات الشبكة

## هيكل المشروع

```
app/
├── src/main/java/com/waseltv/iptv/
│   ├── data/
│   │   ├── model/          # نماذج البيانات
│   │   ├── network/        # طبقة الشبكة وتحليل M3U
│   │   ├── preferences/    # إدارة الإعدادات
│   │   └── repository/     # طبقة البيانات
│   ├── ui/
│   │   ├── components/     # مكونات واجهة المستخدم
│   │   ├── player/         # مشغل الفيديو
│   │   ├── settings/       # شاشة الإعدادات
│   │   ├── theme/          # ألوان وتصميم التطبيق
│   │   └── viewmodel/      # ViewModels
│   └── MainActivity.kt     # النشاط الرئيسي
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:

- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## إخلاء المسؤولية

هذا التطبيق مخصص للاستخدام مع محتوى IPTV قانوني فقط. المطورون غير مسؤولين عن أي استخدام غير قانوني للتطبيق.
