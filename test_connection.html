<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اتصال WASEL-TV</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        
        .test-btn:hover {
            background: #1976D2;
        }
        
        .test-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .url-display {
            font-family: monospace;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار اتصال WASEL-TV</h1>
        
        <div class="info-box">
            <h3>📡 معلومات السيرفر</h3>
            <p><strong>السيرفر:</strong> maventv.one:80</p>
            <p><strong>المستخدم:</strong> odaitv</p>
            <p><strong>النوع:</strong> Xtream Codes IPTV</p>
        </div>
        
        <!-- اختبار API -->
        <div class="test-section">
            <h3>1️⃣ اختبار Xtream Codes API</h3>
            <p>اختبار الاتصال بـ API السيرفر للحصول على معلومات المستخدم</p>
            <div class="url-display" id="api-url">
                http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030
            </div>
            <button class="test-btn" onclick="testAPI()">اختبار API</button>
            <div id="api-result"></div>
        </div>
        
        <!-- اختبار قائمة القنوات -->
        <div class="test-section">
            <h3>2️⃣ اختبار قائمة القنوات</h3>
            <p>اختبار الحصول على قائمة القنوات المباشرة</p>
            <div class="url-display" id="channels-url">
                http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030&action=get_live_streams
            </div>
            <button class="test-btn" onclick="testChannelsList()">اختبار قائمة القنوات</button>
            <div id="channels-result"></div>
        </div>
        
        <!-- اختبار stream محدد -->
        <div class="test-section">
            <h3>3️⃣ اختبار Stream محدد</h3>
            <p>اختبار stream القرآن الكريم (ID: 106997)</p>
            <div class="url-display" id="stream-url">
                http://maventv.one:80/live/odaitv/Odai2030/106997.ts
            </div>
            <button class="test-btn" onclick="testStream()">اختبار Stream</button>
            <div id="stream-result"></div>
        </div>
        
        <!-- اختبار شامل -->
        <div class="test-section">
            <h3>🚀 اختبار شامل</h3>
            <p>تشغيل جميع الاختبارات بالتتابع</p>
            <button class="test-btn" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <div id="all-tests-result"></div>
        </div>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            const btn = event.target;
            
            btn.disabled = true;
            btn.textContent = 'جاري الاختبار...';
            
            resultDiv.innerHTML = '<div class="result loading">🔄 جاري اختبار API...</div>';
            
            try {
                const url = 'http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030';
                
                // محاولة الوصول للـ API
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'no-cors' // لتجنب مشاكل CORS
                });
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ تم الاتصال بـ API بنجاح!<br>
                        <small>ملاحظة: لا يمكن قراءة المحتوى بسبب قيود CORS في المتصفح</small>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ فشل الاتصال بـ API<br>
                        <small>الخطأ: ${error.message}</small>
                    </div>
                `;
            }
            
            btn.disabled = false;
            btn.textContent = 'اختبار API';
        }
        
        async function testChannelsList() {
            const resultDiv = document.getElementById('channels-result');
            const btn = event.target;
            
            btn.disabled = true;
            btn.textContent = 'جاري الاختبار...';
            
            resultDiv.innerHTML = '<div class="result loading">🔄 جاري اختبار قائمة القنوات...</div>';
            
            try {
                const url = 'http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030&action=get_live_streams';
                
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ تم الاتصال بقائمة القنوات بنجاح!<br>
                        <small>ملاحظة: لا يمكن قراءة عدد القنوات بسبب قيود CORS</small>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ فشل الاتصال بقائمة القنوات<br>
                        <small>الخطأ: ${error.message}</small>
                    </div>
                `;
            }
            
            btn.disabled = false;
            btn.textContent = 'اختبار قائمة القنوات';
        }
        
        async function testStream() {
            const resultDiv = document.getElementById('stream-result');
            const btn = event.target;
            
            btn.disabled = true;
            btn.textContent = 'جاري الاختبار...';
            
            resultDiv.innerHTML = '<div class="result loading">🔄 جاري اختبار Stream...</div>';
            
            try {
                const url = 'http://maventv.one:80/live/odaitv/Odai2030/106997.ts';
                
                const response = await fetch(url, {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ Stream متاح!<br>
                        <small>ملاحظة: لا يمكن التحقق من تفاصيل الاستجابة بسبب قيود CORS</small>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ فشل الوصول للـ Stream<br>
                        <small>الخطأ: ${error.message}</small>
                    </div>
                `;
            }
            
            btn.disabled = false;
            btn.textContent = 'اختبار Stream';
        }
        
        async function runAllTests() {
            const resultDiv = document.getElementById('all-tests-result');
            const btn = event.target;
            
            btn.disabled = true;
            btn.textContent = 'جاري التشغيل...';
            
            resultDiv.innerHTML = '<div class="result loading">🚀 جاري تشغيل جميع الاختبارات...</div>';
            
            let results = [];
            
            // اختبار API
            try {
                await fetch('http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030', {mode: 'no-cors'});
                results.push('✅ API يعمل');
            } catch (e) {
                results.push('❌ API لا يعمل');
            }
            
            // تأخير بسيط
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار قائمة القنوات
            try {
                await fetch('http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030&action=get_live_streams', {mode: 'no-cors'});
                results.push('✅ قائمة القنوات تعمل');
            } catch (e) {
                results.push('❌ قائمة القنوات لا تعمل');
            }
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار Stream
            try {
                await fetch('http://maventv.one:80/live/odaitv/Odai2030/106997.ts', {method: 'HEAD', mode: 'no-cors'});
                results.push('✅ Stream يعمل');
            } catch (e) {
                results.push('❌ Stream لا يعمل');
            }
            
            const successCount = results.filter(r => r.includes('✅')).length;
            const totalCount = results.length;
            
            resultDiv.innerHTML = `
                <div class="result ${successCount === totalCount ? 'success' : 'error'}">
                    📊 نتائج الاختبار الشامل:<br>
                    ${results.join('<br>')}<br><br>
                    <strong>النتيجة: ${successCount}/${totalCount} اختبارات نجحت</strong><br>
                    <small>ملاحظة: قيود CORS قد تؤثر على دقة النتائج</small>
                </div>
            `;
            
            btn.disabled = false;
            btn.textContent = 'تشغيل جميع الاختبارات';
        }
        
        // رسالة ترحيب
        window.onload = function() {
            setTimeout(() => {
                alert('🔍 أداة اختبار اتصال WASEL-TV\n\n' +
                      'هذه الأداة تساعدك في تشخيص مشاكل الاتصال بسيرفر Xtream Codes.\n\n' +
                      '⚠️ ملاحظة مهمة:\n' +
                      'بسبب قيود CORS في المتصفح، قد لا تظهر النتائج الدقيقة.\n' +
                      'للاختبار الدقيق، استخدم أدوات التشخيص داخل التطبيق.');
            }, 500);
        };
    </script>
</body>
</html>
