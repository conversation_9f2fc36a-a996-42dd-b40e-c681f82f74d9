@echo off
echo ========================================
echo        بناء تطبيق واصل TV
echo ========================================
echo.

echo 🔧 تنظيف المشروع...
call gradlew clean

echo.
echo 📱 بناء APK للتطبيق...
call gradlew assembleDebug

echo.
echo ✅ تم بناء APK بنجاح!
echo.

echo 📍 مكان ملف APK:
echo app\build\outputs\apk\debug\app-debug.apk
echo.

echo 📋 معلومات التطبيق:
echo اسم التطبيق: واصل TV
echo الإصدار: 1.0.0
echo Package: com.waseltv.iptv.debug
echo.

echo 🚀 لتثبيت التطبيق على الجهاز:
echo adb install app\build\outputs\apk\debug\app-debug.apk
echo.

echo 📱 أو انسخ ملف APK إلى الجهاز وثبته يدوياً
echo.

pause
