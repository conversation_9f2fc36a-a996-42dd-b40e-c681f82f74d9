<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASEL-TV واصل تي في - عرض التطبيق</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2196F3, #BBDEFB);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: #2196F3;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .screen {
            padding: 20px;
            min-height: 500px;
            display: none;
        }
        
        .screen.active {
            display: block;
        }
        
        .channel-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f5f5f5;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .channel-item:hover {
            background: #e3f2fd;
            transform: translateX(-5px);
        }
        
        .channel-icon {
            width: 50px;
            height: 50px;
            background: #2196F3;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-left: 15px;
        }
        
        .channel-info h3 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .channel-info p {
            color: #666;
            font-size: 12px;
        }
        
        .play-btn {
            margin-right: auto;
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
        }
        
        .settings-form {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-group input:focus {
            border-color: #2196F3;
            outline: none;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #1976D2;
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .player-screen {
            background: black;
            color: white;
            text-align: center;
            padding: 50px 20px;
        }
        
        .player-controls {
            margin-top: 30px;
        }
        
        .control-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f5f5f5;
            border-top: 1px solid #ddd;
        }
        
        .nav-btn {
            background: none;
            border: none;
            color: #2196F3;
            font-size: 14px;
            cursor: pointer;
            padding: 5px 10px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📺 WASEL-TV</h1>
            <p>واصل تي في - متصل بسيرفر maventv.one</p>
        </div>
        
        <!-- الشاشة الرئيسية -->
        <div class="screen active" id="main-screen">
            <div class="loading" id="loading" style="display: none;">
                <div class="spinner"></div>
                <p>جاري تحميل القنوات...</p>
            </div>
            
            <div id="channels-list">
                <div class="channel-item" onclick="playChannel('قناة الجزيرة')">
                    <div class="channel-icon">📰</div>
                    <div class="channel-info">
                        <h3>قناة الجزيرة</h3>
                        <p>أخبار • قطر • مباشر من maventv.one</p>
                    </div>
                    <button class="play-btn">▶</button>
                </div>

                <div class="channel-item" onclick="playChannel('العربية')">
                    <div class="channel-icon">📺</div>
                    <div class="channel-info">
                        <h3>العربية</h3>
                        <p>أخبار • السعودية • HD</p>
                    </div>
                    <button class="play-btn">▶</button>
                </div>

                <div class="channel-item" onclick="playChannel('بي إن سبورت 1')">
                    <div class="channel-icon">⚽</div>
                    <div class="channel-info">
                        <h3>بي إن سبورت 1</h3>
                        <p>رياضة • قطر • جودة عالية</p>
                    </div>
                    <button class="play-btn">▶</button>
                </div>

                <div class="channel-item" onclick="playChannel('MBC 1')">
                    <div class="channel-icon">🎬</div>
                    <div class="channel-info">
                        <h3>MBC 1</h3>
                        <p>ترفيه • السعودية • مباشر</p>
                    </div>
                    <button class="play-btn">▶</button>
                </div>

                <div class="channel-item" onclick="playChannel('سبيستون')">
                    <div class="channel-icon">🧸</div>
                    <div class="channel-info">
                        <h3>سبيستون</h3>
                        <p>أطفال • الإمارات • كرتون</p>
                    </div>
                    <button class="play-btn">▶</button>
                </div>
            </div>
        </div>
        
        <!-- شاشة الإعدادات -->
        <div class="screen" id="settings-screen">
            <div class="settings-form">
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 10px 0; color: #1976d2;">معلومات السيرفر</h3>
                    <p style="margin: 5px 0; color: #333;"><strong>السيرفر:</strong> http://maventv.one:80</p>
                    <p style="margin: 5px 0; color: #333;"><strong>المستخدم:</strong> Odaitv</p>
                    <p style="margin: 5px 0; color: #333;"><strong>الحالة:</strong> <span style="color: #4caf50;">متصل ✓</span></p>
                </div>

                <button class="btn" onclick="testConnection()">اختبار الاتصال</button>
                <button class="btn" onclick="refreshChannels()">تحديث قائمة القنوات</button>
                <button class="btn btn-secondary" onclick="showScreen('main-screen')">رجوع</button>

                <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin-top: 20px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">معلومات التطبيق</h3>
                    <p style="margin: 5px 0; color: #666; font-size: 14px;">
                        • التطبيق متصل بسيرفر ثابت<br>
                        • لا حاجة لإدخال معلومات إضافية<br>
                        • يتم تحديث القنوات تلقائياً<br>
                        • جودة عالية ومشاهدة مستقرة
                    </p>
                </div>
            </div>
        </div>
        
        <!-- شاشة المشغل -->
        <div class="screen player-screen" id="player-screen">
            <h2 id="current-channel">قناة تجريبية 1</h2>
            <p>جاري تحميل القناة...</p>
            <div class="player-controls">
                <button class="control-btn" onclick="pausePlay()">⏸️ إيقاف مؤقت</button>
                <button class="control-btn" onclick="showScreen('main-screen')">🔙 رجوع</button>
            </div>
        </div>
        
        <div class="navigation">
            <button class="nav-btn" onclick="showScreen('main-screen')">🏠 الرئيسية</button>
            <button class="nav-btn" onclick="refreshChannels()">🔄 تحديث</button>
            <button class="nav-btn" onclick="showScreen('settings-screen')">⚙️ الإعدادات</button>
        </div>
    </div>
    
    <script>
        function showScreen(screenId) {
            // إخفاء جميع الشاشات
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });
            
            // إظهار الشاشة المطلوبة
            document.getElementById(screenId).classList.add('active');
        }
        
        function playChannel(channelName) {
            document.getElementById('current-channel').textContent = channelName;
            showScreen('player-screen');
            
            // محاكاة تحميل القناة
            setTimeout(() => {
                document.querySelector('#player-screen p').textContent = 'القناة جاهزة للمشاهدة!';
            }, 2000);
        }
        
        function pausePlay() {
            alert('تم إيقاف/تشغيل القناة');
        }
        
        function saveSettings() {
            // السيرفر ثابت، لا حاجة لحفظ إعدادات
            alert('الإعدادات محفوظة تلقائياً!\nالسيرفر: maventv.one');
            showScreen('main-screen');
        }

        function testConnection() {
            alert('جاري اختبار الاتصال...');
            setTimeout(() => {
                alert('✅ تم الاتصال بالسيرفر بنجاح!\nالسيرفر: http://maventv.one:80\nالمستخدم: Odaitv\nالحالة: متصل');
            }, 2000);
        }
        
        function refreshChannels() {
            const loading = document.getElementById('loading');
            const channelsList = document.getElementById('channels-list');
            
            // إظهار شاشة التحميل
            loading.style.display = 'block';
            channelsList.style.display = 'none';
            
            // محاكاة تحميل القنوات
            setTimeout(() => {
                loading.style.display = 'none';
                channelsList.style.display = 'block';
                alert('تم تحديث قائمة القنوات!');
            }, 2000);
        }
        
        // رسالة ترحيب
        setTimeout(() => {
            alert('مرحباً بك في تطبيق WASEL-TV! 📺\n\n✅ متصل بسيرفر: maventv.one\n✅ المستخدم: Odaitv\n✅ جاهز للمشاهدة!\n\nهذا عرض تقديمي للتطبيق يوضح الواجهات والوظائف الأساسية.');
        }, 1000);
    </script>
</body>
</html>
