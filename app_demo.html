<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASEL-TV واصل تي في - عرض التطبيق</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2196F3, #BBDEFB);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: #2196F3;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .screen {
            padding: 20px;
            min-height: 500px;
            display: none;
        }
        
        .screen.active {
            display: block;
        }
        
        .category-tabs {
            display: flex;
            gap: 12px;
            padding: 16px;
            overflow-x: auto;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .category-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 16px;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 80px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .category-tab.active {
            background: #2196F3;
            color: white;
            box-shadow: 0 4px 8px rgba(33,150,243,0.3);
        }

        .category-tab.news.active { background: #FF5722; }
        .category-tab.sports.active { background: #4CAF50; }
        .category-tab.kids.active { background: #FF9800; }
        .category-tab.entertainment.active { background: #9C27B0; }

        .category-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .category-name {
            font-size: 12px;
            font-weight: 500;
        }

        .category-header {
            padding: 16px;
            background: linear-gradient(135deg, #f5f5f5, #e8f5e8);
            margin: 16px;
            border-radius: 12px;
            border-left: 4px solid #2196F3;
        }

        .channel-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 8px 16px;
            background: #f5f5f5;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .channel-item:hover {
            background: #e3f2fd;
            transform: translateX(-5px);
        }
        
        .channel-icon {
            width: 50px;
            height: 50px;
            background: #2196F3;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-left: 15px;
        }
        
        .channel-info h3 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .channel-info p {
            color: #666;
            font-size: 12px;
        }
        
        .play-btn {
            margin-right: auto;
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
        }
        
        .settings-form {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-group input:focus {
            border-color: #2196F3;
            outline: none;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #1976D2;
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .player-screen {
            background: black;
            color: white;
            text-align: center;
            padding: 50px 20px;
        }
        
        .player-controls {
            margin-top: 30px;
        }
        
        .control-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f5f5f5;
            border-top: 1px solid #ddd;
        }
        
        .nav-btn {
            background: none;
            border: none;
            color: #2196F3;
            font-size: 14px;
            cursor: pointer;
            padding: 5px 10px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📺 WASEL-TV</h1>
            <p>واصل تي في - Xtream Codes IPTV</p>
        </div>
        
        <!-- الشاشة الرئيسية -->
        <div class="screen active" id="main-screen">
            <div class="loading" id="loading" style="display: none;">
                <div class="spinner"></div>
                <p>جاري تحميل القنوات...</p>
            </div>

            <!-- شريط الفئات -->
            <div class="category-tabs">
                <div class="category-tab news active" onclick="selectCategory('news')">
                    <div class="category-icon">📰</div>
                    <div class="category-name">أخبار</div>
                </div>
                <div class="category-tab sports" onclick="selectCategory('sports')">
                    <div class="category-icon">⚽</div>
                    <div class="category-name">رياضة</div>
                </div>
                <div class="category-tab kids" onclick="selectCategory('kids')">
                    <div class="category-icon">🧸</div>
                    <div class="category-name">أطفال</div>
                </div>
                <div class="category-tab entertainment" onclick="selectCategory('entertainment')">
                    <div class="category-icon">🎬</div>
                    <div class="category-name">ترفيه</div>
                </div>
            </div>

            <!-- رأس الفئة -->
            <div class="category-header" id="category-header">
                <h2 style="margin: 0 0 8px 0; color: #FF5722;">📰 قنوات الأخبار</h2>
                <p style="margin: 0; color: #666;">قنوات الأخبار والتقارير الإخبارية</p>
                <p style="margin: 4px 0 0 0; color: #888; font-size: 14px;">8 قنوات متاحة • بث مباشر من Xtream Codes</p>
            </div>

            <!-- قائمة القنوات -->
            <div id="channels-list">
                <!-- قنوات الأخبار -->
                <div class="news-channels">
                    <div class="channel-item" onclick="playChannel('قناة الجزيرة')">
                        <div class="channel-icon">📰</div>
                        <div class="channel-info">
                            <h3>قناة الجزيرة</h3>
                            <p>أخبار • قطر • مباشر من Xtream Codes</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('العربية')">
                        <div class="channel-icon">📰</div>
                        <div class="channel-info">
                            <h3>العربية</h3>
                            <p>أخبار • السعودية • HD</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('BBC Arabic')">
                        <div class="channel-icon">📰</div>
                        <div class="channel-info">
                            <h3>BBC Arabic</h3>
                            <p>أخبار • بريطانيا • Stream ID: 107000</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('سكاي نيوز عربية')">
                        <div class="channel-icon">📰</div>
                        <div class="channel-info">
                            <h3>سكاي نيوز عربية</h3>
                            <p>أخبار • الإمارات • Stream ID: 107001</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('الحدث')">
                        <div class="channel-icon">📰</div>
                        <div class="channel-info">
                            <h3>الحدث</h3>
                            <p>أخبار • السعودية • Stream ID: 107002</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('الجزيرة الوثائقية')">
                        <div class="channel-icon">📰</div>
                        <div class="channel-info">
                            <h3>الجزيرة الوثائقية</h3>
                            <p>أخبار • قطر • Stream ID: 107003</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('فرانس 24')">
                        <div class="channel-icon">📰</div>
                        <div class="channel-info">
                            <h3>فرانس 24</h3>
                            <p>أخبار • فرنسا • Stream ID: 107004</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>
                </div>

                <!-- قنوات الرياضة (مخفية افتراضياً) -->
                <div class="sports-channels" style="display: none;">
                    <div class="channel-item" onclick="playChannel('بي إن سبورت 1')">
                        <div class="channel-icon">⚽</div>
                        <div class="channel-info">
                            <h3>بي إن سبورت 1</h3>
                            <p>رياضة • قطر • جودة عالية</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('بي إن سبورت 2')">
                        <div class="channel-icon">⚽</div>
                        <div class="channel-info">
                            <h3>بي إن سبورت 2</h3>
                            <p>رياضة • قطر • مباريات مباشرة</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('أبو ظبي الرياضية')">
                        <div class="channel-icon">⚽</div>
                        <div class="channel-info">
                            <h3>أبو ظبي الرياضية</h3>
                            <p>رياضة • الإمارات • محلية</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>
                </div>

                <!-- قنوات الأطفال (مخفية افتراضياً) -->
                <div class="kids-channels" style="display: none;">
                    <div class="channel-item" onclick="playChannel('سبيستون')">
                        <div class="channel-icon">🧸</div>
                        <div class="channel-info">
                            <h3>سبيستون</h3>
                            <p>أطفال • الإمارات • كرتون</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('طيور الجنة')">
                        <div class="channel-icon">🧸</div>
                        <div class="channel-info">
                            <h3>طيور الجنة</h3>
                            <p>أطفال • الأردن • أناشيد</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('براعم')">
                        <div class="channel-icon">🧸</div>
                        <div class="channel-info">
                            <h3>براعم</h3>
                            <p>أطفال • قطر • تعليمية</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>
                </div>

                <!-- قنوات الترفيه (مخفية افتراضياً) -->
                <div class="entertainment-channels" style="display: none;">
                    <div class="channel-item" onclick="playChannel('MBC 1')">
                        <div class="channel-icon">🎬</div>
                        <div class="channel-info">
                            <h3>MBC 1</h3>
                            <p>ترفيه • السعودية • مسلسلات</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('MBC 2')">
                        <div class="channel-icon">🎬</div>
                        <div class="channel-info">
                            <h3>MBC 2</h3>
                            <p>ترفيه • السعودية • أفلام</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>

                    <div class="channel-item" onclick="playChannel('دبي')">
                        <div class="channel-icon">🎬</div>
                        <div class="channel-info">
                            <h3>دبي</h3>
                            <p>ترفيه • الإمارات • متنوعة</p>
                        </div>
                        <button class="play-btn">▶</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- شاشة الإعدادات -->
        <div class="screen" id="settings-screen">
            <div class="settings-form">
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 10px 0; color: #1976d2;">🔗 Xtream Codes IPTV</h3>
                    <p style="margin: 5px 0; color: #333;"><strong>📡 السيرفر:</strong> http://maventv.one:80</p>
                    <p style="margin: 5px 0; color: #333;"><strong>👤 المستخدم:</strong> odaitv</p>
                    <p style="margin: 5px 0; color: #333;"><strong>🔐 كلمة المرور:</strong> Odai2030</p>
                    <p style="margin: 5px 0; color: #333;"><strong>✅ النوع:</strong> Xtream Codes API</p>
                    <p style="margin: 5px 0; color: #333;"><strong>📊 الحالة:</strong> <span style="color: #4caf50;">متصل ✓</span></p>
                </div>

                <button class="btn" onclick="testConnection()">اختبار الاتصال</button>
                <button class="btn" onclick="refreshChannels()">تحديث قائمة القنوات</button>
                <button class="btn btn-secondary" onclick="showScreen('main-screen')">رجوع</button>

                <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin-top: 20px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">📱 معلومات التطبيق</h3>
                    <p style="margin: 5px 0; color: #666; font-size: 14px;">
                        🔗 يستخدم Xtream Codes API حصرياً<br>
                        📡 متصل بسيرفر maventv.one ثابت<br>
                        🔄 تحديث تلقائي لقائمة القنوات<br>
                        📺 دعم البث المباشر عالي الجودة<br>
                        🎬 مشغل فيديو متقدم مع ExoPlayer
                    </p>
                </div>
            </div>
        </div>
        
        <!-- شاشة المشغل -->
        <div class="screen player-screen" id="player-screen">
            <h2 id="current-channel">قناة تجريبية 1</h2>
            <p>جاري تحميل القناة...</p>
            <div class="player-controls">
                <button class="control-btn" onclick="pausePlay()">⏸️ إيقاف مؤقت</button>
                <button class="control-btn" onclick="showScreen('main-screen')">🔙 رجوع</button>
            </div>
        </div>
        
        <div class="navigation">
            <button class="nav-btn" onclick="showScreen('main-screen')">🏠 الرئيسية</button>
            <button class="nav-btn" onclick="refreshChannels()">🔄 تحديث</button>
            <button class="nav-btn" onclick="showScreen('settings-screen')">⚙️ الإعدادات</button>
        </div>
    </div>
    
    <script>
        function showScreen(screenId) {
            // إخفاء جميع الشاشات
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });

            // إظهار الشاشة المطلوبة
            document.getElementById(screenId).classList.add('active');
        }

        function selectCategory(categoryId) {
            // إزالة active من جميع التبويبات
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // إضافة active للتبويب المختار
            document.querySelector(`.category-tab.${categoryId}`).classList.add('active');

            // إخفاء جميع مجموعات القنوات
            document.querySelectorAll('[class$="-channels"]').forEach(group => {
                group.style.display = 'none';
            });

            // إظهار مجموعة القنوات المختارة
            document.querySelector(`.${categoryId}-channels`).style.display = 'block';

            // تحديث رأس الفئة
            const headers = {
                'news': {
                    title: '📰 قنوات الأخبار',
                    description: 'قنوات الأخبار والتقارير الإخبارية',
                    count: '8 قنوات متاحة • بث مباشر من Xtream Codes',
                    color: '#FF5722'
                },
                'sports': {
                    title: '⚽ القنوات الرياضية',
                    description: 'القنوات الرياضية والمباريات المباشرة',
                    count: '7 قنوات متاحة • جودة HD',
                    color: '#4CAF50'
                },
                'kids': {
                    title: '🧸 قنوات الأطفال',
                    description: 'قنوات الأطفال والكرتون',
                    count: '7 قنوات متاحة • محتوى آمن',
                    color: '#FF9800'
                },
                'entertainment': {
                    title: '🎬 قنوات الترفيه',
                    description: 'قنوات الترفيه والأفلام والمسلسلات',
                    count: '8 قنوات متاحة • محتوى متنوع',
                    color: '#9C27B0'
                }
            };

            const header = headers[categoryId];
            if (header) {
                document.getElementById('category-header').innerHTML = `
                    <h2 style="margin: 0 0 8px 0; color: ${header.color};">${header.title}</h2>
                    <p style="margin: 0; color: #666;">${header.description}</p>
                    <p style="margin: 4px 0 0 0; color: #888; font-size: 14px;">${header.count}</p>
                `;
            }
        }
        
        function playChannel(channelName) {
            document.getElementById('current-channel').textContent = channelName;
            showScreen('player-screen');
            
            // محاكاة تحميل القناة
            setTimeout(() => {
                document.querySelector('#player-screen p').textContent = 'القناة جاهزة للمشاهدة!';
            }, 2000);
        }
        
        function pausePlay() {
            alert('تم إيقاف/تشغيل القناة');
        }
        
        function saveSettings() {
            // السيرفر ثابت، لا حاجة لحفظ إعدادات
            alert('الإعدادات محفوظة تلقائياً!\nالسيرفر: maventv.one');
            showScreen('main-screen');
        }

        function testConnection() {
            alert('جاري اختبار الاتصال بـ Xtream Codes API...');
            setTimeout(() => {
                alert('✅ تم الاتصال بـ Xtream Codes بنجاح!\n\n🔗 النوع: Xtream Codes IPTV\n📡 السيرفر: http://maventv.one:80\n👤 المستخدم: odaitv\n📊 الحالة: متصل ونشط\n📺 القنوات: متاحة');
            }, 2000);
        }
        
        function refreshChannels() {
            const loading = document.getElementById('loading');
            const channelsList = document.getElementById('channels-list');

            // إظهار شاشة التحميل
            loading.style.display = 'block';
            channelsList.style.display = 'none';

            // محاكاة اختبار السيرفر وتحميل القنوات
            let step = 0;
            const steps = [
                'جاري الاتصال بـ Xtream Codes API...',
                'اختبار المصادقة مع السيرفر...',
                'تحليل استجابة Xtream Codes...',
                'البحث عن القنوات المباشرة...',
                'تحميل قائمة القنوات من API...'
            ];

            const updateStep = () => {
                if (step < steps.length) {
                    document.querySelector('#loading p').textContent = steps[step];
                    step++;
                    setTimeout(updateStep, 800);
                } else {
                    loading.style.display = 'none';
                    channelsList.style.display = 'block';
                    alert('✅ تم تحديث قائمة القنوات!\n\n🔗 تم الاتصال بـ Xtream Codes API\n📺 تم تحميل القنوات المباشرة\n🎬 روابط البث جاهزة للتشغيل');
                }
            };

            updateStep();
        }
        
        // رسالة ترحيب
        setTimeout(() => {
            alert('مرحباً بك في تطبيق WASEL-TV! 📺\n\n🔗 يستخدم Xtream Codes API حصرياً\n📡 متصل بسيرفر: maventv.one:80\n👤 المستخدم: odaitv\n\n📂 الفئات المتاحة:\n📰 أخبار (8 قنوات)\n⚽ رياضة (7 قنوات)\n🧸 أطفال (7 قنوات)\n🎬 ترفيه (8 قنوات)\n\n✅ جاهز للمشاهدة!\n🎯 جميع القنوات تستخدم Stream IDs حقيقية\n\nجرب التنقل بين الفئات المختلفة!');
        }, 1000);
    </script>
</body>
</html>
