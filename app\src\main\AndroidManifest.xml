<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Internet permission for IPTV streaming -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- Wake lock to keep screen on during video playback -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Additional permissions for IPTV streaming -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    
    <!-- External storage for downloading playlists -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="28" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.WASELTV"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        
        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.WASELTV"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- Video Player Activity -->
        <activity
            android:name=".ui.player.VideoPlayerActivity"
            android:exported="false"
            android:theme="@style/Theme.WASELTV.Fullscreen"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden" />
            
        <!-- Settings Activity -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.WASELTV"
            android:screenOrientation="portrait" />

        <!-- Diagnostics Activity -->
        <activity
            android:name=".ui.diagnostics.DiagnosticsActivity"
            android:exported="false"
            android:theme="@style/Theme.WASELTV"
            android:screenOrientation="portrait" />

    </application>

</manifest>
