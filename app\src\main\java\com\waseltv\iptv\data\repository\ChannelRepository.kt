package com.waseltv.iptv.data.repository

import android.content.Context
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.data.model.ServerConfig
import com.waseltv.iptv.data.network.M3UParser
import com.waseltv.iptv.data.preferences.PreferencesManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ChannelRepository(private val context: Context) {
    private val preferencesManager = PreferencesManager()
    private val m3uParser = M3UParser()

    suspend fun getChannels(): List<Channel> = withContext(Dispatchers.IO) {
        val serverConfig = preferencesManager.getServerConfig(context)
        
        if (serverConfig?.playlistUrl.isNullOrEmpty()) {
            // Return demo channels if no server configured
            return@withContext getDemoChannels()
        }
        
        try {
            // Parse M3U playlist from server
            val playlistUrl = serverConfig!!.playlistUrl!!
            return@withContext m3uParser.parseFromUrl(playlistUrl)
        } catch (e: Exception) {
            // Return demo channels on error
            return@withContext getDemoChannels()
        }
    }
    
    private fun getDemoChannels(): List<Channel> {
        return listOf(
            Channel(
                id = "1",
                name = "قناة تجريبية 1",
                url = "https://example.com/stream1.m3u8",
                logo = null,
                group = "عام",
                language = "العربية",
                country = "مصر",
                description = "قناة تجريبية للاختبار"
            ),
            Channel(
                id = "2",
                name = "قناة تجريبية 2",
                url = "https://example.com/stream2.m3u8",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "السعودية",
                description = "قناة أخبار تجريبية"
            ),
            Channel(
                id = "3",
                name = "قناة تجريبية 3",
                url = "https://example.com/stream3.m3u8",
                logo = null,
                group = "رياضة",
                language = "العربية",
                country = "الإمارات",
                description = "قناة رياضية تجريبية"
            ),
            Channel(
                id = "4",
                name = "قناة تجريبية 4",
                url = "https://example.com/stream4.m3u8",
                logo = null,
                group = "ترفيه",
                language = "العربية",
                country = "لبنان",
                description = "قناة ترفيهية تجريبية"
            ),
            Channel(
                id = "5",
                name = "قناة تجريبية 5",
                url = "https://example.com/stream5.m3u8",
                logo = null,
                group = "أطفال",
                language = "العربية",
                country = "الأردن",
                description = "قناة أطفال تجريبية"
            )
        )
    }
}
