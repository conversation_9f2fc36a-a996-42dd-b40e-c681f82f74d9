package com.waseltv.iptv.data.repository

import android.content.Context
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.data.model.ServerConfig
import com.waseltv.iptv.data.network.IPTVApiService
import com.waseltv.iptv.data.preferences.PreferencesManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ChannelRepository(private val context: Context) {
    private val preferencesManager = PreferencesManager()
    private val iptvApiService = IPTVApiService()

    // إعدادات السيرفر الثابتة
    companion object {
        private const val SERVER_URL = "http://maventv.one:80"
        private const val USERNAME = "Odaitv"
        private const val PASSWORD = "Odai2030"
    }

    suspend fun getChannels(): List<Channel> = withContext(Dispatchers.IO) {
        return@withContext iptvApiService.getChannels()
    }

    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        return@withContext iptvApiService.testConnection()
    }
    
    private fun getTestChannels(): List<Channel> {
        // قنوات تجريبية مع روابط حقيقية من السيرفر
        return listOf(
            Channel(
                id = "1",
                name = "قناة الجزيرة",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1.ts",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "قطر",
                description = "قناة إخبارية عربية"
            ),
            Channel(
                id = "2",
                name = "العربية",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/2.ts",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "السعودية",
                description = "قناة إخبارية عربية"
            ),
            Channel(
                id = "3",
                name = "بي إن سبورت 1",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/3.ts",
                logo = null,
                group = "رياضة",
                language = "العربية",
                country = "قطر",
                description = "قناة رياضية"
            ),
            Channel(
                id = "4",
                name = "MBC 1",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/4.ts",
                logo = null,
                group = "ترفيه",
                language = "العربية",
                country = "السعودية",
                description = "قناة ترفيهية"
            ),
            Channel(
                id = "5",
                name = "سبيستون",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/5.ts",
                logo = null,
                group = "أطفال",
                language = "العربية",
                country = "الإمارات",
                description = "قناة أطفال"
            )
        )
    }

    // دالة للحصول على معلومات السيرفر
    fun getServerInfo(): ServerConfig {
        return ServerConfig(
            serverUrl = SERVER_URL,
            username = USERNAME,
            password = PASSWORD,
            playlistUrl = "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u_plus&output=ts"
        )
    }
}
