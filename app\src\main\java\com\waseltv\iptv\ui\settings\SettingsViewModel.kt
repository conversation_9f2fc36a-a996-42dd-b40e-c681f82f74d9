package com.waseltv.iptv.ui.settings

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.waseltv.iptv.data.model.ServerConfig
import com.waseltv.iptv.data.preferences.PreferencesManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class SettingsUiState(
    val serverUrl: String = "",
    val username: String = "",
    val password: String = "",
    val playlistUrl: String = "",
    val isLoading: Boolean = false,
    val isSaved: Boolean = false,
    val error: String? = null
)

class SettingsViewModel(application: Application) : AndroidViewModel(application) {
    private val preferencesManager = PreferencesManager()
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    init {
        loadSettings()
    }
    
    private fun loadSettings() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val serverConfig = preferencesManager.getServerConfig(getApplication())
                _uiState.value = _uiState.value.copy(
                    serverUrl = serverConfig?.serverUrl ?: "",
                    username = serverConfig?.username ?: "",
                    password = serverConfig?.password ?: "",
                    playlistUrl = serverConfig?.playlistUrl ?: "",
                    isLoading = false,
                    error = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }
    
    fun updateServerUrl(serverUrl: String) {
        _uiState.value = _uiState.value.copy(serverUrl = serverUrl)
    }
    
    fun updateUsername(username: String) {
        _uiState.value = _uiState.value.copy(username = username)
    }
    
    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(password = password)
    }
    
    fun updatePlaylistUrl(playlistUrl: String) {
        _uiState.value = _uiState.value.copy(playlistUrl = playlistUrl)
    }
    
    fun saveSettings() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val currentState = _uiState.value
                val serverConfig = ServerConfig(
                    serverUrl = currentState.serverUrl,
                    username = currentState.username.takeIf { it.isNotBlank() },
                    password = currentState.password.takeIf { it.isNotBlank() },
                    playlistUrl = currentState.playlistUrl.takeIf { it.isNotBlank() }
                )
                
                preferencesManager.saveServerConfig(getApplication(), serverConfig)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isSaved = true,
                    error = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "حدث خطأ أثناء حفظ الإعدادات"
                )
            }
        }
    }
    
    fun clearSettings() {
        viewModelScope.launch {
            try {
                preferencesManager.clearServerConfig(getApplication())
                _uiState.value = SettingsUiState()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "حدث خطأ أثناء مسح الإعدادات"
                )
            }
        }
    }
}
