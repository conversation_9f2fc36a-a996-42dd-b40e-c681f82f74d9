package com.waseltv.iptv.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.data.repository.ChannelRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class MainUiState(
    val channels: List<Channel> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val debugInfo: String? = null
)

class MainViewModel(application: Application) : AndroidViewModel(application) {
    private val channelRepository = ChannelRepository(application)
    
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    fun loadChannels() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null,
                debugInfo = "جاري الاتصال بالسيرفر..."
            )

            try {
                // اختبار الاتصال أولاً
                val isConnected = channelRepository.testConnection()
                _uiState.value = _uiState.value.copy(
                    debugInfo = if (isConnected) "تم الاتصال بالسيرفر، جاري تحميل القنوات..."
                               else "فشل الاتصال، استخدام القنوات الافتراضية..."
                )

                val channels = channelRepository.getChannels()
                _uiState.value = _uiState.value.copy(
                    channels = channels,
                    isLoading = false,
                    error = null,
                    debugInfo = "تم تحميل ${channels.size} قناة"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "حدث خطأ غير معروف",
                    debugInfo = "خطأ: ${e.message}"
                )
            }
        }
    }
    
    fun refreshChannels() {
        loadChannels()
    }
}
