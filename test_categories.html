<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأقسام - WASEL-TV</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 30px;
        }
        
        .categories-container {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .category-card {
            flex: 1;
            min-width: 150px;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .category-card.active {
            border-color: white;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .category-card.news {
            background: linear-gradient(135deg, #FF5722, #FF7043);
            color: white;
        }
        
        .category-card.sports {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
        }
        
        .category-card.kids {
            background: linear-gradient(135deg, #FF9800, #FFB74D);
            color: white;
        }
        
        .category-card.entertainment {
            background: linear-gradient(135deg, #9C27B0, #BA68C8);
            color: white;
        }
        
        .category-icon {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }
        
        .category-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .category-count {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .channels-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .channels-header {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .channel-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .channel-icon {
            font-size: 24px;
        }
        
        .channel-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .channel-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 اختبار الأقسام الرئيسية - WASEL-TV</h1>
        
        <div class="info-box">
            <h3>✅ الأقسام الأربع المطلوبة فقط</h3>
            <p>اضغط على أي قسم لعرض قنواته</p>
        </div>
        
        <!-- الأقسام الأربع الرئيسية -->
        <div class="categories-container">
            <div class="category-card news active" onclick="selectCategory('news')">
                <span class="category-icon">📰</span>
                <div class="category-name">أخبار</div>
                <div class="category-count">8 قنوات</div>
            </div>
            
            <div class="category-card sports" onclick="selectCategory('sports')">
                <span class="category-icon">⚽</span>
                <div class="category-name">رياضة</div>
                <div class="category-count">7 قنوات</div>
            </div>
            
            <div class="category-card kids" onclick="selectCategory('kids')">
                <span class="category-icon">🧸</span>
                <div class="category-name">أطفال</div>
                <div class="category-count">7 قنوات</div>
            </div>
            
            <div class="category-card entertainment" onclick="selectCategory('entertainment')">
                <span class="category-icon">🎬</span>
                <div class="category-name">ترفيه</div>
                <div class="category-count">8 قنوات</div>
            </div>
        </div>
        
        <!-- قسم عرض القنوات -->
        <div class="channels-section" id="channels-section">
            <div class="channels-header" id="channels-header">
                📰 قنوات الأخبار
            </div>
            <div id="channels-list">
                <!-- سيتم ملء القنوات هنا بواسطة JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // بيانات القنوات لكل قسم
        const channelsData = {
            news: {
                title: '📰 قنوات الأخبار',
                channels: [
                    { name: 'القرآن الكريم', id: '106997', description: 'قناة القرآن الكريم' },
                    { name: 'الجزيرة مباشر', id: '106998', description: 'قناة الجزيرة الإخبارية' },
                    { name: 'العربية', id: '106999', description: 'قناة العربية الإخبارية' },
                    { name: 'BBC Arabic', id: '107000', description: 'بي بي سي العربية' },
                    { name: 'سكاي نيوز عربية', id: '107001', description: 'سكاي نيوز العربية' },
                    { name: 'الحدث', id: '107002', description: 'قناة الحدث' },
                    { name: 'الجزيرة الوثائقية', id: '107003', description: 'الجزيرة الوثائقية' },
                    { name: 'فرانس 24', id: '107004', description: 'فرانس 24 العربية' }
                ]
            },
            sports: {
                title: '⚽ القنوات الرياضية',
                channels: [
                    { name: 'بي إن سبورت 1 HD', id: '107010', description: 'بي إن سبورت 1 عالي الجودة' },
                    { name: 'بي إن سبورت 2 HD', id: '107011', description: 'بي إن سبورت 2 عالي الجودة' },
                    { name: 'بي إن سبورت 3 HD', id: '107012', description: 'بي إن سبورت 3 عالي الجودة' },
                    { name: 'أبو ظبي الرياضية', id: '107013', description: 'قناة أبو ظبي الرياضية' },
                    { name: 'الكأس', id: '107014', description: 'قناة الكأس الرياضية' },
                    { name: 'السعودية الرياضية', id: '107015', description: 'القناة السعودية الرياضية' },
                    { name: 'دبي الرياضية', id: '107016', description: 'قناة دبي الرياضية' }
                ]
            },
            kids: {
                title: '🧸 قنوات الأطفال',
                channels: [
                    { name: 'سبيستون', id: '107020', description: 'قناة سبيستون للأطفال' },
                    { name: 'طيور الجنة', id: '107021', description: 'قناة طيور الجنة' },
                    { name: 'براعم', id: '107022', description: 'قناة براعم للأطفال' },
                    { name: 'كرتون نتورك بالعربية', id: '107023', description: 'كرتون نتورك العربية' },
                    { name: 'ديزني جونيور', id: '107024', description: 'قناة ديزني جونيور' },
                    { name: 'نيكلوديون', id: '107025', description: 'قناة نيكلوديون' },
                    { name: 'MBC 3', id: '107026', description: 'إم بي سي 3 للأطفال' }
                ]
            },
            entertainment: {
                title: '🎬 قنوات الترفيه',
                channels: [
                    { name: 'MBC 1 HD', id: '107030', description: 'إم بي سي 1 عالي الجودة' },
                    { name: 'MBC 2 HD', id: '107031', description: 'إم بي سي 2 عالي الجودة' },
                    { name: 'MBC 4 HD', id: '107032', description: 'إم بي سي 4 عالي الجودة' },
                    { name: 'دبي', id: '107033', description: 'قناة دبي' },
                    { name: 'الحياة', id: '107034', description: 'قناة الحياة' },
                    { name: 'LBC', id: '107035', description: 'قناة الـ LBC' },
                    { name: 'الفضائية السورية', id: '107036', description: 'الفضائية السورية' },
                    { name: 'أبو ظبي', id: '107037', description: 'قناة أبو ظبي' }
                ]
            }
        };
        
        function selectCategory(categoryId) {
            // إزالة active من جميع الأقسام
            document.querySelectorAll('.category-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // إضافة active للقسم المختار
            document.querySelector(`.category-card.${categoryId}`).classList.add('active');
            
            // تحديث عنوان القنوات
            const categoryData = channelsData[categoryId];
            document.getElementById('channels-header').textContent = categoryData.title;
            
            // تحديث قائمة القنوات
            const channelsList = document.getElementById('channels-list');
            channelsList.innerHTML = '';
            
            categoryData.channels.forEach(channel => {
                const channelElement = document.createElement('div');
                channelElement.className = 'channel-item';
                channelElement.innerHTML = `
                    <div class="channel-icon">${getChannelIcon(categoryId)}</div>
                    <div class="channel-info">
                        <h4>${channel.name}</h4>
                        <p>${channel.description} • Stream ID: ${channel.id}</p>
                    </div>
                `;
                channelsList.appendChild(channelElement);
            });
        }
        
        function getChannelIcon(categoryId) {
            const icons = {
                news: '📰',
                sports: '⚽',
                kids: '🧸',
                entertainment: '🎬'
            };
            return icons[categoryId] || '📺';
        }
        
        // تحميل قنوات الأخبار افتراضياً
        window.onload = function() {
            selectCategory('news');
            
            setTimeout(() => {
                alert('✅ اختبار الأقسام الرئيسية\n\n' +
                      'هذا يوضح كيف ستظهر الأقسام الأربع في التطبيق:\n\n' +
                      '📰 أخبار\n' +
                      '⚽ رياضة\n' +
                      '🧸 أطفال\n' +
                      '🎬 ترفيه\n\n' +
                      'اضغط على أي قسم لعرض قنواته!');
            }, 500);
        };
    </script>
</body>
</html>
