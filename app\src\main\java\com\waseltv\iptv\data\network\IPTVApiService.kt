package com.waseltv.iptv.data.network

import com.waseltv.iptv.data.model.Channel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONArray
import org.json.JSONObject
import java.util.UUID
import java.util.concurrent.TimeUnit

class IPTVApiService {
    
    companion object {
        private const val SERVER_URL = "http://maventv.one:80"
        private const val USERNAME = "Odaitv"
        private const val PASSWORD = "Odai2030"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    suspend fun getChannels(): List<Channel> = withContext(Dispatchers.IO) {
        try {
            // جرب الحصول على قائمة القنوات من API
            val channels = mutableListOf<Channel>()
            
            // جرب عدة طرق للحصول على القنوات
            val urls = listOf(
                "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams",
                "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u_plus&output=ts",
                "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u"
            )
            
            for (url in urls) {
                try {
                    val channelList = fetchChannelsFromUrl(url)
                    if (channelList.isNotEmpty()) {
                        return@withContext channelList
                    }
                } catch (e: Exception) {
                    // جرب الرابط التالي
                    continue
                }
            }
            
            // إذا فشلت جميع المحاولات، أرجع قنوات افتراضية
            return@withContext getDefaultChannels()
            
        } catch (e: Exception) {
            return@withContext getDefaultChannels()
        }
    }
    
    private suspend fun fetchChannelsFromUrl(url: String): List<Channel> = withContext(Dispatchers.IO) {
        val request = Request.Builder()
            .url(url)
            .addHeader("User-Agent", "WASEL-TV/1.0")
            .build()
        
        val response = httpClient.newCall(request).execute()
        if (!response.isSuccessful) {
            throw Exception("HTTP ${response.code}")
        }
        
        val content = response.body?.string() ?: throw Exception("Empty response")
        
        return@withContext when {
            url.contains("player_api.php") -> parseJsonChannels(content)
            url.contains("m3u") -> parseM3UChannels(content)
            else -> emptyList()
        }
    }
    
    private fun parseJsonChannels(jsonContent: String): List<Channel> {
        try {
            val channels = mutableListOf<Channel>()
            val jsonArray = JSONArray(jsonContent)
            
            for (i in 0 until jsonArray.length()) {
                val channelObj = jsonArray.getJSONObject(i)
                val streamId = channelObj.optString("stream_id", "")
                val name = channelObj.optString("name", "قناة غير معروفة")
                val categoryId = channelObj.optString("category_id", "")
                val streamIcon = channelObj.optString("stream_icon", "")
                
                if (streamId.isNotEmpty()) {
                    val streamUrl = "$SERVER_URL/live/$USERNAME/$PASSWORD/$streamId.ts"
                    
                    val channel = Channel(
                        id = streamId,
                        name = name,
                        url = streamUrl,
                        logo = streamIcon.takeIf { it.isNotEmpty() },
                        group = getCategoryName(categoryId),
                        language = "العربية",
                        country = "عربي",
                        description = "قناة مباشرة"
                    )
                    channels.add(channel)
                }
            }
            
            return channels
        } catch (e: Exception) {
            return emptyList()
        }
    }
    
    private fun parseM3UChannels(m3uContent: String): List<Channel> {
        val m3uParser = M3UParser()
        return m3uParser.parseM3UContent(m3uContent)
    }
    
    private fun getCategoryName(categoryId: String): String {
        return when (categoryId) {
            "1" -> "أخبار"
            "2" -> "رياضة"
            "3" -> "ترفيه"
            "4" -> "أطفال"
            "5" -> "أفلام"
            "6" -> "مسلسلات"
            "7" -> "موسيقى"
            "8" -> "وثائقي"
            else -> "عام"
        }
    }
    
    private fun getDefaultChannels(): List<Channel> {
        return listOf(
            Channel(
                id = "1001",
                name = "قناة الجزيرة",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1001.ts",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "قطر"
            ),
            Channel(
                id = "1002",
                name = "العربية",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1002.ts",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "السعودية"
            ),
            Channel(
                id = "1003",
                name = "بي إن سبورت 1",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1003.ts",
                logo = null,
                group = "رياضة",
                language = "العربية",
                country = "قطر"
            ),
            Channel(
                id = "1004",
                name = "MBC 1",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1004.ts",
                logo = null,
                group = "ترفيه",
                language = "العربية",
                country = "السعودية"
            ),
            Channel(
                id = "1005",
                name = "سبيستون",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1005.ts",
                logo = null,
                group = "أطفال",
                language = "العربية",
                country = "الإمارات"
            )
        )
    }
    
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            val testUrl = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams"
            val request = Request.Builder()
                .url(testUrl)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()
            
            val response = httpClient.newCall(request).execute()
            return@withContext response.isSuccessful
        } catch (e: Exception) {
            return@withContext false
        }
    }
}
