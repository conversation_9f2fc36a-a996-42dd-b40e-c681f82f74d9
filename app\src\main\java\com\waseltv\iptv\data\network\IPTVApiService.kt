package com.waseltv.iptv.data.network

import android.util.Log
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.utils.ServerTester
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONArray
import org.json.JSONObject
import java.util.UUID
import java.util.concurrent.TimeUnit

class IPTVApiService {

    companion object {
        private const val TAG = "WASEL-TV-API"
        private const val SERVER_URL = "http://maventv.one:80"
        private const val USERNAME = "Odaitv"
        private const val PASSWORD = "Odai2030"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    suspend fun getChannels(): List<Channel> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "بدء تحميل القنوات من السيرفر")

            // أولاً، اختبر جميع endpoints
            val testResults = ServerTester.testAllEndpoints()
            for (result in testResults) {
                Log.d(TAG, "${result.name}: ${result.status}")
            }

            // جرب الحصول على قنوات من endpoints التي تعمل
            val workingResults = testResults.filter { it.success }

            if (workingResults.isNotEmpty()) {
                Log.d(TAG, "وُجدت ${workingResults.size} endpoints تعمل")

                for (result in workingResults) {
                    try {
                        val channelList = fetchChannelsFromUrl(result.url)
                        if (channelList.isNotEmpty()) {
                            Log.d(TAG, "تم العثور على ${channelList.size} قناة من ${result.name}")
                            return@withContext channelList
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "فشل في تحليل ${result.name}: ${e.message}")
                        continue
                    }
                }
            }

            // جرب اختبار stream URLs مباشرة
            Log.d(TAG, "جاري اختبار stream URLs مباشرة...")
            val testStreams = ServerTester.testStreamUrls()
            if (testStreams.isNotEmpty()) {
                Log.d(TAG, "تم العثور على ${testStreams.size} stream يعمل")
                return@withContext testStreams
            }

            // إذا فشل كل شيء، استخدم القنوات الافتراضية
            Log.d(TAG, "فشلت جميع المحاولات، استخدام القنوات الافتراضية")
            return@withContext getDefaultChannels()

        } catch (e: Exception) {
            Log.e(TAG, "خطأ عام في تحميل القنوات", e)
            return@withContext getDefaultChannels()
        }
    }
    
    private suspend fun fetchChannelsFromUrl(url: String): List<Channel> = withContext(Dispatchers.IO) {
        val request = Request.Builder()
            .url(url)
            .addHeader("User-Agent", "WASEL-TV/1.0")
            .addHeader("Accept", "*/*")
            .addHeader("Connection", "keep-alive")
            .build()

        val response = httpClient.newCall(request).execute()
        val content = response.body?.string() ?: ""

        println("WASEL-TV: Response code: ${response.code}")
        println("WASEL-TV: Content length: ${content.length}")
        println("WASEL-TV: Content preview: ${content.take(200)}")

        if (!response.isSuccessful) {
            throw Exception("HTTP ${response.code}: $content")
        }

        if (content.isEmpty()) {
            throw Exception("Empty response")
        }

        // تحقق من رسائل الخطأ الشائعة
        if (content.contains("Invalid Authorization") ||
            content.contains("404 Error") ||
            content.contains("Access Denied")) {
            throw Exception("Authentication failed: $content")
        }

        return@withContext when {
            url.contains("player_api.php") -> parseJsonChannels(content)
            url.contains("m3u") || content.trim().startsWith("#EXTM3U") -> parseM3UChannels(content)
            content.trim().startsWith("[") || content.trim().startsWith("{") -> parseJsonChannels(content)
            else -> {
                println("WASEL-TV: Unknown content type, trying M3U parser")
                parseM3UChannels(content)
            }
        }
    }
    
    private fun parseJsonChannels(jsonContent: String): List<Channel> {
        try {
            val channels = mutableListOf<Channel>()
            val jsonArray = JSONArray(jsonContent)
            
            for (i in 0 until jsonArray.length()) {
                val channelObj = jsonArray.getJSONObject(i)
                val streamId = channelObj.optString("stream_id", "")
                val name = channelObj.optString("name", "قناة غير معروفة")
                val categoryId = channelObj.optString("category_id", "")
                val streamIcon = channelObj.optString("stream_icon", "")
                
                if (streamId.isNotEmpty()) {
                    val streamUrl = "$SERVER_URL/live/$USERNAME/$PASSWORD/$streamId.ts"
                    
                    val channel = Channel(
                        id = streamId,
                        name = name,
                        url = streamUrl,
                        logo = streamIcon.takeIf { it.isNotEmpty() },
                        group = getCategoryName(categoryId),
                        language = "العربية",
                        country = "عربي",
                        description = "قناة مباشرة"
                    )
                    channels.add(channel)
                }
            }
            
            return channels
        } catch (e: Exception) {
            return emptyList()
        }
    }
    
    private fun parseM3UChannels(m3uContent: String): List<Channel> {
        val m3uParser = M3UParser()
        return m3uParser.parseM3UContent(m3uContent)
    }
    
    private fun getCategoryName(categoryId: String): String {
        return when (categoryId) {
            "1" -> "أخبار"
            "2" -> "رياضة"
            "3" -> "ترفيه"
            "4" -> "أطفال"
            "5" -> "أفلام"
            "6" -> "مسلسلات"
            "7" -> "موسيقى"
            "8" -> "وثائقي"
            else -> "عام"
        }
    }
    
    private fun getDefaultChannels(): List<Channel> {
        // جرب تنسيقات مختلفة للروابط
        val streamFormats = listOf(
            "$SERVER_URL/live/$USERNAME/$PASSWORD/{id}.ts",
            "$SERVER_URL/live/$USERNAME/$PASSWORD/{id}.m3u8",
            "$SERVER_URL/live/$USERNAME/$PASSWORD/{id}",
            "$SERVER_URL/{USERNAME}/{PASSWORD}/{id}.ts",
            "$SERVER_URL/{USERNAME}/{PASSWORD}/{id}.m3u8"
        )

        val channels = mutableListOf<Channel>()
        val channelData = listOf(
            Triple("1", "قناة تجريبية 1", "عام"),
            Triple("2", "قناة تجريبية 2", "أخبار"),
            Triple("3", "قناة تجريبية 3", "رياضة"),
            Triple("100", "قناة تجريبية 100", "ترفيه"),
            Triple("1001", "قناة تجريبية 1001", "أطفال"),
            Triple("10001", "قناة تجريبية 10001", "أفلام")
        )

        // إنشاء قنوات بتنسيقات مختلفة
        for ((index, format) in streamFormats.withIndex()) {
            for ((id, name, group) in channelData) {
                val streamUrl = format.replace("{id}", id)
                    .replace("{USERNAME}", USERNAME)
                    .replace("{PASSWORD}", PASSWORD)

                channels.add(Channel(
                    id = "${id}_${index}",
                    name = "$name (تنسيق ${index + 1})",
                    url = streamUrl,
                    logo = null,
                    group = group,
                    language = "العربية",
                    country = "تجريبي",
                    description = "قناة تجريبية - تنسيق ${index + 1}"
                ))
            }
        }

        // إضافة بعض القنوات المجانية المعروفة للاختبار
        channels.addAll(listOf(
            Channel(
                id = "free_1",
                name = "Big Buck Bunny (اختبار)",
                url = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                logo = null,
                group = "اختبار",
                language = "English",
                country = "Test",
                description = "فيديو اختبار مجاني"
            ),
            Channel(
                id = "free_2",
                name = "Sintel (اختبار)",
                url = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4",
                logo = null,
                group = "اختبار",
                language = "English",
                country = "Test",
                description = "فيديو اختبار مجاني آخر"
            )
        ))

        return channels
    }
    
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        val testUrls = listOf(
            "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams",
            "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u",
            "$SERVER_URL/xmltv.php?username=$USERNAME&password=$PASSWORD",
            "$SERVER_URL"
        )

        for (url in testUrls) {
            try {
                println("WASEL-TV: Testing connection to: $url")
                val request = Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "WASEL-TV/1.0")
                    .build()

                val response = httpClient.newCall(request).execute()
                val content = response.body?.string() ?: ""

                println("WASEL-TV: Response code: ${response.code}")
                println("WASEL-TV: Content: ${content.take(100)}")

                if (response.isSuccessful &&
                    !content.contains("Invalid Authorization") &&
                    !content.contains("404 Error")) {
                    println("WASEL-TV: Connection successful!")
                    return@withContext true
                }
            } catch (e: Exception) {
                println("WASEL-TV: Connection failed: ${e.message}")
                continue
            }
        }

        return@withContext false
    }

    suspend fun getServerInfo(): String = withContext(Dispatchers.IO) {
        try {
            val infoUrl = "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD"
            val request = Request.Builder()
                .url(infoUrl)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .build()

            val response = httpClient.newCall(request).execute()
            val content = response.body?.string() ?: ""

            return@withContext if (response.isSuccessful) {
                "Server Info: ${content.take(200)}"
            } else {
                "Server Error: ${response.code} - $content"
            }
        } catch (e: Exception) {
            return@withContext "Connection Error: ${e.message}"
        }
    }
}
