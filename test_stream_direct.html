<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Stream مباشر - WASEL-TV</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 30px;
        }
        
        .video-container {
            background: #000;
            border-radius: 10px;
            margin-bottom: 20px;
            position: relative;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        video {
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }
        
        .placeholder {
            color: white;
            text-align: center;
            font-size: 18px;
        }
        
        .stream-selector {
            margin-bottom: 20px;
        }
        
        .stream-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .stream-btn:hover {
            background: #1976D2;
        }
        
        .stream-btn.active {
            background: #4CAF50;
        }
        
        .stream-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .url-display {
            font-family: monospace;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .control-btn {
            background: #FF5722;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 16px;
        }
        
        .control-btn:hover {
            background: #E64A19;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📺 اختبار Stream مباشر - WASEL-TV</h1>
        
        <div class="info-box">
            <h3>🎯 اختبار تشغيل Streams مباشرة في المتصفح</h3>
            <p>هذه الأداة تحاول تشغيل stream URLs مباشرة باستخدام HTML5 video player</p>
        </div>
        
        <!-- مشغل الفيديو -->
        <div class="video-container">
            <video id="videoPlayer" controls style="display: none;">
                متصفحك لا يدعم تشغيل الفيديو
            </video>
            <div id="placeholder" class="placeholder">
                📺 اختر stream لبدء التشغيل
            </div>
        </div>
        
        <!-- معلومات الـ Stream -->
        <div class="stream-info">
            <h3>📡 معلومات الـ Stream الحالي</h3>
            <div id="current-stream-info">لم يتم اختيار stream بعد</div>
            <div class="url-display" id="current-url">-</div>
            <div id="stream-status"></div>
        </div>
        
        <!-- أزرار اختيار الـ Streams -->
        <div class="stream-selector">
            <h3>🎬 اختر Stream للاختبار:</h3>
            
            <h4>📰 قنوات الأخبار:</h4>
            <button class="stream-btn" onclick="loadStream('106997', 'القرآن الكريم')">القرآن الكريم</button>
            <button class="stream-btn" onclick="loadStream('106998', 'الجزيرة')">الجزيرة</button>
            <button class="stream-btn" onclick="loadStream('106999', 'العربية')">العربية</button>
            
            <h4>⚽ قنوات الرياضة:</h4>
            <button class="stream-btn" onclick="loadStream('107010', 'بي إن سبورت 1')">بي إن سبورت 1</button>
            <button class="stream-btn" onclick="loadStream('107011', 'بي إن سبورت 2')">بي إن سبورت 2</button>
            
            <h4>🧸 قنوات الأطفال:</h4>
            <button class="stream-btn" onclick="loadStream('107020', 'سبيستون')">سبيستون</button>
            <button class="stream-btn" onclick="loadStream('107021', 'طيور الجنة')">طيور الجنة</button>
            
            <h4>🎬 قنوات الترفيه:</h4>
            <button class="stream-btn" onclick="loadStream('107030', 'MBC 1')">MBC 1</button>
            <button class="stream-btn" onclick="loadStream('107031', 'MBC 2')">MBC 2</button>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="controls">
            <button class="control-btn" onclick="playVideo()">▶ تشغيل</button>
            <button class="control-btn" onclick="pauseVideo()">⏸ إيقاف مؤقت</button>
            <button class="control-btn" onclick="stopVideo()">⏹ إيقاف</button>
            <button class="control-btn" onclick="reloadStream()">🔄 إعادة تحميل</button>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://maventv.one:80';
        const USERNAME = 'odaitv';
        const PASSWORD = 'Odai2030';
        
        let currentStreamId = null;
        let currentStreamName = null;
        
        function createStreamUrl(streamId) {
            return `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${streamId}.ts`;
        }
        
        function loadStream(streamId, streamName) {
            currentStreamId = streamId;
            currentStreamName = streamName;
            
            const streamUrl = createStreamUrl(streamId);
            const video = document.getElementById('videoPlayer');
            const placeholder = document.getElementById('placeholder');
            const statusDiv = document.getElementById('stream-status');
            
            // تحديث المعلومات
            document.getElementById('current-stream-info').textContent = `${streamName} (ID: ${streamId})`;
            document.getElementById('current-url').textContent = streamUrl;
            
            // إزالة active من جميع الأزرار
            document.querySelectorAll('.stream-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // إضافة active للزر المختار
            event.target.classList.add('active');
            
            // عرض حالة التحميل
            statusDiv.innerHTML = '<div class="status loading">🔄 جاري تحميل الـ Stream...</div>';
            
            // إعداد الفيديو
            video.src = streamUrl;
            video.style.display = 'block';
            placeholder.style.display = 'none';
            
            // معالجة أحداث الفيديو
            video.onloadstart = function() {
                statusDiv.innerHTML = '<div class="status loading">📡 جاري الاتصال بالسيرفر...</div>';
            };
            
            video.oncanplay = function() {
                statusDiv.innerHTML = '<div class="status success">✅ Stream جاهز للتشغيل!</div>';
            };
            
            video.onplay = function() {
                statusDiv.innerHTML = '<div class="status success">▶ يتم التشغيل الآن</div>';
            };
            
            video.onerror = function(e) {
                let errorMsg = 'خطأ غير معروف';
                if (video.error) {
                    switch(video.error.code) {
                        case 1:
                            errorMsg = 'تم إلغاء التحميل';
                            break;
                        case 2:
                            errorMsg = 'خطأ في الشبكة';
                            break;
                        case 3:
                            errorMsg = 'خطأ في فك التشفير';
                            break;
                        case 4:
                            errorMsg = 'تنسيق غير مدعوم';
                            break;
                    }
                }
                statusDiv.innerHTML = `<div class="status error">❌ فشل تحميل الـ Stream<br>الخطأ: ${errorMsg}</div>`;
            };
            
            video.onstalled = function() {
                statusDiv.innerHTML = '<div class="status loading">⏳ الـ Stream متوقف، جاري إعادة المحاولة...</div>';
            };
            
            video.onwaiting = function() {
                statusDiv.innerHTML = '<div class="status loading">⏳ جاري التخزين المؤقت...</div>';
            };
            
            // محاولة تشغيل تلقائي
            video.load();
        }
        
        function playVideo() {
            const video = document.getElementById('videoPlayer');
            if (video.src) {
                video.play().catch(e => {
                    console.error('فشل التشغيل:', e);
                    document.getElementById('stream-status').innerHTML = 
                        '<div class="status error">❌ فشل التشغيل: ' + e.message + '</div>';
                });
            } else {
                alert('اختر stream أولاً');
            }
        }
        
        function pauseVideo() {
            const video = document.getElementById('videoPlayer');
            video.pause();
        }
        
        function stopVideo() {
            const video = document.getElementById('videoPlayer');
            video.pause();
            video.currentTime = 0;
            document.getElementById('stream-status').innerHTML = '<div class="status">⏹ تم إيقاف التشغيل</div>';
        }
        
        function reloadStream() {
            if (currentStreamId && currentStreamName) {
                loadStream(currentStreamId, currentStreamName);
            } else {
                alert('اختر stream أولاً');
            }
        }
        
        // رسالة ترحيب
        window.onload = function() {
            setTimeout(() => {
                alert('📺 اختبار Stream مباشر\n\n' +
                      'هذه الأداة تحاول تشغيل streams مباشرة في المتصفح.\n\n' +
                      '⚠️ ملاحظات مهمة:\n' +
                      '• قد لا تعمل جميع الـ streams في المتصفح\n' +
                      '• بعض الـ streams تحتاج مشغل متخصص\n' +
                      '• النتائج قد تختلف عن التطبيق\n\n' +
                      'جرب streams مختلفة لمعرفة أيها يعمل!');
            }, 500);
        };
    </script>
</body>
</html>
